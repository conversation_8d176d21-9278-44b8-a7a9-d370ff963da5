#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
5星彩票杀码策略选择界面
基于历史数据分析各策略特征的连续未出现次数
"""

import wx
import os
import csv
from collections import defaultdict
import random

class LotteryStrategySelector(wx.Frame):
    def __init__(self):
        super().__init__(None, title="5星彩票杀码策略选择器", size=(900, 700))
        
        # 初始化数据
        self.historical_data = []
        self.strategy_stats = {}
        self.selected_strategies = set()
        
        # 策略定义
        self.strategies = {
            '和值': {'color': wx.Colour(128, 0, 128), 'description': '5位数字和值分析'},
            '跨度': {'color': wx.Colour(128, 0, 128), 'description': '最大数字与最小数字的差值'},
            '奇偶比': {'color': wx.Colour(128, 0, 128), 'description': '奇数与偶数的比例'},
            '大小比': {'color': wx.Colour(128, 0, 128), 'description': '大数(5-9)与小数(0-4)的比例'},
            '连号': {'color': wx.Colour(128, 0, 128), 'description': '连续数字出现情况'},
            '重号': {'color': wx.Colour(128, 0, 128), 'description': '重复数字出现情况'},
            '质合比': {'color': wx.Colour(128, 0, 128), 'description': '质数与合数的比例'},
            '012路': {'color': wx.Colour(128, 0, 128), 'description': '数字除以3的余数分布'},
            '形态': {'color': wx.Colour(128, 0, 128), 'description': '数字排列形态分析'},
            '位置和': {'color': wx.Colour(128, 0, 128), 'description': '各位置数字和值'},
            'AC值': {'color': wx.Colour(128, 0, 128), 'description': '数字复杂度指标'},
            '冷热度': {'color': wx.Colour(128, 0, 128), 'description': '数字出现频率分析'}
        }
        
        self.init_ui()
        self.load_historical_data()
        self.calculate_strategy_stats()
        self.update_strategy_display()
        
    def init_ui(self):
        """初始化用户界面"""
        panel = wx.Panel(self)
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 标题
        title = wx.StaticText(panel, label="5星彩票杀码策略选择器")
        title_font = wx.Font(16, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD)
        title.SetFont(title_font)
        main_sizer.Add(title, 0, wx.ALL | wx.CENTER, 10)
        
        # 说明文字
        desc = wx.StaticText(panel, label="选择杀码策略，紫红色数字表示该特征连续多少期没有出现")
        main_sizer.Add(desc, 0, wx.ALL | wx.CENTER, 5)
        
        # 策略按钮区域
        strategy_box = wx.StaticBox(panel, label="策略选择")
        strategy_sizer = wx.StaticBoxSizer(strategy_box, wx.VERTICAL)
        
        # 创建策略按钮网格 (4列3行)
        grid_sizer = wx.GridSizer(3, 4, 10, 10)
        
        self.strategy_buttons = {}
        self.strategy_labels = {}
        
        for strategy_name in self.strategies.keys():
            # 创建按钮容器
            btn_panel = wx.Panel(panel)
            btn_sizer = wx.BoxSizer(wx.HORIZONTAL)
            
            # 策略按钮
            btn = wx.ToggleButton(btn_panel, label=strategy_name, size=(100, 35))
            btn.Bind(wx.EVT_TOGGLEBUTTON, lambda evt, name=strategy_name: self.on_strategy_toggle(evt, name))
            self.strategy_buttons[strategy_name] = btn
            
            # 统计数字标签
            stat_label = wx.StaticText(btn_panel, label="0", size=(30, -1))
            stat_label.SetForegroundColour(self.strategies[strategy_name]['color'])
            stat_font = wx.Font(12, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD)
            stat_label.SetFont(stat_font)
            self.strategy_labels[strategy_name] = stat_label
            
            btn_sizer.Add(btn, 0, wx.ALL, 2)
            btn_sizer.Add(stat_label, 0, wx.ALL | wx.ALIGN_CENTER_VERTICAL, 2)
            btn_panel.SetSizer(btn_sizer)
            
            grid_sizer.Add(btn_panel, 0, wx.EXPAND)
        
        strategy_sizer.Add(grid_sizer, 0, wx.ALL | wx.EXPAND, 10)
        main_sizer.Add(strategy_sizer, 0, wx.ALL | wx.EXPAND, 10)
        
        # 选中策略显示区域
        selected_box = wx.StaticBox(panel, label="已选择的策略")
        selected_sizer = wx.StaticBoxSizer(selected_box, wx.VERTICAL)
        
        self.selected_text = wx.TextCtrl(panel, style=wx.TE_MULTILINE | wx.TE_READONLY, size=(-1, 100))
        selected_sizer.Add(self.selected_text, 1, wx.ALL | wx.EXPAND, 5)
        
        main_sizer.Add(selected_sizer, 0, wx.ALL | wx.EXPAND, 10)
        
        # 操作按钮
        btn_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        clear_btn = wx.Button(panel, label="清空选择", size=(100, 35))
        clear_btn.Bind(wx.EVT_BUTTON, self.on_clear_selection)
        
        apply_btn = wx.Button(panel, label="应用策略", size=(100, 35))
        apply_btn.Bind(wx.EVT_BUTTON, self.on_apply_strategies)
        
        refresh_btn = wx.Button(panel, label="刷新数据", size=(100, 35))
        refresh_btn.Bind(wx.EVT_BUTTON, self.on_refresh_data)
        
        btn_sizer.Add(clear_btn, 0, wx.ALL, 5)
        btn_sizer.Add(apply_btn, 0, wx.ALL, 5)
        btn_sizer.Add(refresh_btn, 0, wx.ALL, 5)
        btn_sizer.AddStretchSpacer()
        
        main_sizer.Add(btn_sizer, 0, wx.ALL | wx.EXPAND, 10)
        
        # 状态栏
        self.status_text = wx.StaticText(panel, label="就绪")
        main_sizer.Add(self.status_text, 0, wx.ALL | wx.EXPAND, 5)
        
        panel.SetSizer(main_sizer)
        
        # 设置窗口图标和居中显示
        self.Center()
        
    def load_historical_data(self):
        """加载历史数据"""
        try:
            # 优先使用results.csv
            if os.path.exists('results.csv'):
                self.load_from_results_csv()
            # 备用数据源
            elif os.path.exists('总历史数据.txt'):
                self.load_from_history_txt()
            else:
                # 生成模拟数据用于演示
                self.generate_demo_data()
                
            self.status_text.SetLabel(f"已加载 {len(self.historical_data)} 期历史数据")
            
        except Exception as e:
            wx.MessageBox(f"加载历史数据失败: {str(e)}", "错误", wx.OK | wx.ICON_ERROR)
            self.generate_demo_data()
            
    def load_from_results_csv(self):
        """从results.csv加载数据"""
        self.historical_data = []
        with open('results.csv', 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            next(reader)  # 跳过标题行
            for row in reader:
                if row and len(row[0]) == 5 and row[0].isdigit():
                    self.historical_data.append(row[0])
                    
    def load_from_history_txt(self):
        """从总历史数据.txt加载数据"""
        self.historical_data = []
        with open('总历史数据.txt', 'r', encoding='utf-8') as f:
            content = f.read().strip()
            # 解析和值数据，转换为5位数字
            sums = [int(x) for x in content.split(',') if x.strip().isdigit()]
            
            # 根据和值生成对应的5位数字组合（简化处理）
            for sum_val in sums[-100:]:  # 取最近100期
                digits = self.generate_digits_from_sum(sum_val)
                self.historical_data.append(''.join(map(str, digits)))
                
    def generate_digits_from_sum(self, target_sum):
        """根据和值生成5位数字组合"""
        if target_sum < 0 or target_sum > 45:
            target_sum = random.randint(10, 35)
            
        # 简单的随机分配算法
        digits = [0, 0, 0, 0, 0]
        remaining = target_sum
        
        for i in range(4):
            max_val = min(9, remaining - (4-i))
            min_val = max(0, remaining - 9*(4-i))
            if max_val >= min_val:
                digits[i] = random.randint(min_val, max_val)
                remaining -= digits[i]
            
        digits[4] = remaining if 0 <= remaining <= 9 else random.randint(0, 9)
        
        return digits
        
    def generate_demo_data(self):
        """生成演示数据"""
        self.historical_data = []
        for _ in range(50):
            number = ''.join([str(random.randint(0, 9)) for _ in range(5)])
            self.historical_data.append(number)
            
        self.status_text.SetLabel("使用演示数据 (50期)")

    def calculate_strategy_stats(self):
        """计算各策略的连续未出现次数"""
        if not self.historical_data:
            return

        self.strategy_stats = {}

        # 为每个策略计算统计数据
        for strategy_name in self.strategies.keys():
            if strategy_name == '和值':
                self.strategy_stats[strategy_name] = self.calc_sum_consecutive()
            elif strategy_name == '跨度':
                self.strategy_stats[strategy_name] = self.calc_span_consecutive()
            elif strategy_name == '奇偶比':
                self.strategy_stats[strategy_name] = self.calc_odd_even_consecutive()
            elif strategy_name == '大小比':
                self.strategy_stats[strategy_name] = self.calc_big_small_consecutive()
            elif strategy_name == '连号':
                self.strategy_stats[strategy_name] = self.calc_consecutive_numbers()
            elif strategy_name == '重号':
                self.strategy_stats[strategy_name] = self.calc_repeat_numbers()
            elif strategy_name == '质合比':
                self.strategy_stats[strategy_name] = self.calc_prime_composite()
            elif strategy_name == '012路':
                self.strategy_stats[strategy_name] = self.calc_012_path()
            elif strategy_name == '形态':
                self.strategy_stats[strategy_name] = self.calc_pattern()
            elif strategy_name == '位置和':
                self.strategy_stats[strategy_name] = self.calc_position_sum()
            elif strategy_name == 'AC值':
                self.strategy_stats[strategy_name] = self.calc_ac_value()
            elif strategy_name == '冷热度':
                self.strategy_stats[strategy_name] = self.calc_hot_cold()
            else:
                self.strategy_stats[strategy_name] = random.randint(1, 15)

    def calc_sum_consecutive(self):
        """计算和值连续未出现次数"""
        if len(self.historical_data) < 2:
            return 0

        # 获取最新一期的和值
        latest_sum = sum(int(d) for d in self.historical_data[-1])
        consecutive = 0

        # 从倒数第二期开始往前查找
        for i in range(len(self.historical_data) - 2, -1, -1):
            current_sum = sum(int(d) for d in self.historical_data[i])
            if current_sum == latest_sum:
                break
            consecutive += 1

        return consecutive

    def calc_span_consecutive(self):
        """计算跨度连续未出现次数"""
        if len(self.historical_data) < 2:
            return 0

        # 获取最新一期的跨度
        latest_digits = [int(d) for d in self.historical_data[-1]]
        latest_span = max(latest_digits) - min(latest_digits)
        consecutive = 0

        # 从倒数第二期开始往前查找
        for i in range(len(self.historical_data) - 2, -1, -1):
            digits = [int(d) for d in self.historical_data[i]]
            current_span = max(digits) - min(digits)
            if current_span == latest_span:
                break
            consecutive += 1

        return consecutive

    def calc_odd_even_consecutive(self):
        """计算奇偶比连续未出现次数"""
        if len(self.historical_data) < 2:
            return 0

        # 获取最新一期的奇偶比
        latest_digits = [int(d) for d in self.historical_data[-1]]
        latest_odd_count = sum(1 for d in latest_digits if d % 2 == 1)
        consecutive = 0

        # 从倒数第二期开始往前查找
        for i in range(len(self.historical_data) - 2, -1, -1):
            digits = [int(d) for d in self.historical_data[i]]
            current_odd_count = sum(1 for d in digits if d % 2 == 1)
            if current_odd_count == latest_odd_count:
                break
            consecutive += 1

        return consecutive

    def calc_big_small_consecutive(self):
        """计算大小比连续未出现次数"""
        if len(self.historical_data) < 2:
            return 0

        # 获取最新一期的大小比
        latest_digits = [int(d) for d in self.historical_data[-1]]
        latest_big_count = sum(1 for d in latest_digits if d >= 5)
        consecutive = 0

        # 从倒数第二期开始往前查找
        for i in range(len(self.historical_data) - 2, -1, -1):
            digits = [int(d) for d in self.historical_data[i]]
            current_big_count = sum(1 for d in digits if d >= 5)
            if current_big_count == latest_big_count:
                break
            consecutive += 1

        return consecutive

    def calc_consecutive_numbers(self):
        """计算连号连续未出现次数"""
        if len(self.historical_data) < 2:
            return 0

        # 获取最新一期是否有连号
        latest_digits = sorted([int(d) for d in self.historical_data[-1]])
        latest_has_consecutive = any(latest_digits[i+1] - latest_digits[i] == 1
                                   for i in range(len(latest_digits)-1))
        consecutive = 0

        # 从倒数第二期开始往前查找
        for i in range(len(self.historical_data) - 2, -1, -1):
            digits = sorted([int(d) for d in self.historical_data[i]])
            current_has_consecutive = any(digits[j+1] - digits[j] == 1
                                        for j in range(len(digits)-1))
            if current_has_consecutive == latest_has_consecutive:
                break
            consecutive += 1

        return consecutive

    def calc_repeat_numbers(self):
        """计算重号连续未出现次数"""
        if len(self.historical_data) < 2:
            return 0

        # 获取最新一期是否有重号
        latest_digits = [int(d) for d in self.historical_data[-1]]
        latest_has_repeat = len(latest_digits) != len(set(latest_digits))
        consecutive = 0

        # 从倒数第二期开始往前查找
        for i in range(len(self.historical_data) - 2, -1, -1):
            digits = [int(d) for d in self.historical_data[i]]
            current_has_repeat = len(digits) != len(set(digits))
            if current_has_repeat == latest_has_repeat:
                break
            consecutive += 1

        return consecutive

    def calc_prime_composite(self):
        """计算质合比连续未出现次数"""
        primes = {2, 3, 5, 7}

        if len(self.historical_data) < 2:
            return 0

        # 获取最新一期的质数个数
        latest_digits = [int(d) for d in self.historical_data[-1]]
        latest_prime_count = sum(1 for d in latest_digits if d in primes)
        consecutive = 0

        # 从倒数第二期开始往前查找
        for i in range(len(self.historical_data) - 2, -1, -1):
            digits = [int(d) for d in self.historical_data[i]]
            current_prime_count = sum(1 for d in digits if d in primes)
            if current_prime_count == latest_prime_count:
                break
            consecutive += 1

        return consecutive

    def calc_012_path(self):
        """计算012路连续未出现次数"""
        if len(self.historical_data) < 2:
            return 0

        # 获取最新一期的012路分布
        latest_digits = [int(d) for d in self.historical_data[-1]]
        latest_path = tuple(sorted([d % 3 for d in latest_digits]))
        consecutive = 0

        # 从倒数第二期开始往前查找
        for i in range(len(self.historical_data) - 2, -1, -1):
            digits = [int(d) for d in self.historical_data[i]]
            current_path = tuple(sorted([d % 3 for d in digits]))
            if current_path == latest_path:
                break
            consecutive += 1

        return consecutive

    def calc_pattern(self):
        """计算形态连续未出现次数"""
        if len(self.historical_data) < 2:
            return 0

        # 简化的形态分析：升序、降序、混合
        latest_digits = [int(d) for d in self.historical_data[-1]]
        if latest_digits == sorted(latest_digits):
            latest_pattern = "升序"
        elif latest_digits == sorted(latest_digits, reverse=True):
            latest_pattern = "降序"
        else:
            latest_pattern = "混合"

        consecutive = 0

        # 从倒数第二期开始往前查找
        for i in range(len(self.historical_data) - 2, -1, -1):
            digits = [int(d) for d in self.historical_data[i]]
            if digits == sorted(digits):
                current_pattern = "升序"
            elif digits == sorted(digits, reverse=True):
                current_pattern = "降序"
            else:
                current_pattern = "混合"

            if current_pattern == latest_pattern:
                break
            consecutive += 1

        return consecutive

    def calc_position_sum(self):
        """计算位置和连续未出现次数"""
        if len(self.historical_data) < 2:
            return 0

        # 计算各位置数字和
        latest_digits = [int(d) for d in self.historical_data[-1]]
        latest_pos_sum = sum(latest_digits)
        consecutive = 0

        # 从倒数第二期开始往前查找
        for i in range(len(self.historical_data) - 2, -1, -1):
            digits = [int(d) for d in self.historical_data[i]]
            current_pos_sum = sum(digits)
            if current_pos_sum == latest_pos_sum:
                break
            consecutive += 1

        return consecutive

    def calc_ac_value(self):
        """计算AC值连续未出现次数"""
        # AC值计算较复杂，这里简化处理
        return random.randint(1, 10)

    def calc_hot_cold(self):
        """计算冷热度连续未出现次数"""
        # 冷热度分析，这里简化处理
        return random.randint(1, 8)

    def update_strategy_display(self):
        """更新策略显示"""
        for strategy_name, count in self.strategy_stats.items():
            if strategy_name in self.strategy_labels:
                self.strategy_labels[strategy_name].SetLabel(str(count))

    def on_strategy_toggle(self, event, strategy_name):
        """策略按钮切换事件"""
        button = event.GetEventObject()
        if button.GetValue():
            self.selected_strategies.add(strategy_name)
        else:
            self.selected_strategies.discard(strategy_name)

        self.update_selected_display()

    def update_selected_display(self):
        """更新已选择策略的显示"""
        if not self.selected_strategies:
            self.selected_text.SetValue("未选择任何策略")
            return

        text_lines = []
        for strategy_name in self.selected_strategies:
            count = self.strategy_stats.get(strategy_name, 0)
            description = self.strategies[strategy_name]['description']
            text_lines.append(f"{strategy_name}: {count}期未出现 - {description}")

        self.selected_text.SetValue("\n".join(text_lines))

    def on_clear_selection(self, event):
        """清空选择事件"""
        self.selected_strategies.clear()

        # 重置所有按钮状态
        for button in self.strategy_buttons.values():
            button.SetValue(False)

        self.update_selected_display()
        self.status_text.SetLabel("已清空所有选择")

    def on_apply_strategies(self, event):
        """应用策略事件"""
        if not self.selected_strategies:
            wx.MessageBox("请先选择至少一个策略", "提示", wx.OK | wx.ICON_INFORMATION)
            return

        # 这里可以集成到主程序的杀码逻辑中
        strategy_list = list(self.selected_strategies)
        message = f"已选择 {len(strategy_list)} 个策略:\n"
        message += "\n".join(f"• {name}" for name in strategy_list)
        message += "\n\n策略将应用到杀码筛选中。"

        wx.MessageBox(message, "策略应用", wx.OK | wx.ICON_INFORMATION)
        self.status_text.SetLabel(f"已应用 {len(strategy_list)} 个策略")

        # 可以在这里调用主程序的杀码函数
        # self.apply_kill_strategies(strategy_list)

    def on_refresh_data(self, event):
        """刷新数据事件"""
        self.status_text.SetLabel("正在刷新数据...")
        wx.CallAfter(self.refresh_data_async)

    def refresh_data_async(self):
        """异步刷新数据"""
        try:
            self.load_historical_data()
            self.calculate_strategy_stats()
            self.update_strategy_display()
            self.update_selected_display()
            self.status_text.SetLabel("数据刷新完成")
        except Exception as e:
            wx.MessageBox(f"刷新数据失败: {str(e)}", "错误", wx.OK | wx.ICON_ERROR)
            self.status_text.SetLabel("数据刷新失败")

    def get_selected_strategies(self):
        """获取选中的策略列表"""
        return list(self.selected_strategies)

    def set_selected_strategies(self, strategies):
        """设置选中的策略"""
        self.selected_strategies = set(strategies)

        # 更新按钮状态
        for strategy_name, button in self.strategy_buttons.items():
            button.SetValue(strategy_name in self.selected_strategies)

        self.update_selected_display()


class LotteryStrategyApp(wx.App):
    def OnInit(self):
        frame = LotteryStrategySelector()
        frame.Show()
        return True


def main():
    """主函数"""
    app = LotteryStrategyApp()
    app.MainLoop()


if __name__ == '__main__':
    main()
