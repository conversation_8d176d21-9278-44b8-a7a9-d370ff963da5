#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试5星彩票杀码策略选择器
"""

import sys
import os

def test_data_loading():
    """测试数据加载功能"""
    print("测试数据加载功能...")
    
    try:
        from lottery_strategy_selector import LotteryStrategySelector
        import wx
        
        # 创建应用但不显示主循环
        app = wx.App(False)
        
        # 创建策略选择器
        selector = LotteryStrategySelector()
        
        # 测试数据加载
        print(f"加载的数据期数: {len(selector.historical_data)}")
        print(f"状态信息: {selector.status_text.GetLabel()}")
        
        if selector.historical_data:
            print(f"最新3期: {selector.historical_data[-3:]}")
            print(f"最早3期: {selector.historical_data[:3]}")
        
        # 测试策略统计
        print("\n策略统计:")
        for name, count in selector.strategy_stats.items():
            print(f"  {name}: {count}期")
        
        app.Destroy()
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 40)
    print("5星彩票策略选择器简单测试")
    print("=" * 40)
    
    # 检查数据文件
    print("\n检查数据文件:")
    files_to_check = [
        r"D:\辉达挂机软件 - 副本\OpenCode\HASHFFC.txt",
        "HASHFFC.txt",
        "results.csv",
        "总历史数据.txt"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(f"  ✓ {file_path} (大小: {len(content)} 字符)")
            except Exception as e:
                print(f"  ⚠ {file_path} (读取失败: {e})")
        else:
            print(f"  ✗ {file_path} (不存在)")
    
    # 测试数据加载
    print("\n" + "=" * 40)
    success = test_data_loading()
    
    print("\n" + "=" * 40)
    if success:
        print("测试完成！")
    else:
        print("测试失败！")
    
    return success

if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(f"程序异常: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")
