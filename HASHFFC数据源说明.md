# HASHFFC.txt数据源集成说明

## 🎯 更新概述

已成功将5星彩票杀码策略选择器的数据源修改为指定的HASHFFC.txt文件，并保持了完整的向下兼容性和错误处理机制。

## 📁 数据源优先级

系统按以下优先级加载数据源：

1. **主数据源**: `D:\辉达挂机软件 - 副本\OpenCode\HASHFFC.txt`
2. **本地备份**: `HASHFFC.txt` (当前目录)
3. **备用数据源1**: `results.csv`
4. **备用数据源2**: `总历史数据.txt`
5. **演示数据**: 自动生成的随机数据

## 🔧 支持的数据格式

### 格式1: 每行一个5位数字
```
12345
67890
13579
24680
11223
```

### 格式2: 逗号分隔的5位数字
```
12345,67890,13579,24680,11223
```

### 格式3: 空格分隔的单个数字
```
1 2 3 4 5 6 7 8 9 0 1 3 5 7 9 2 4 6 8 0 1 1 2 2 3
```
*注：每5个数字组成一期数据*

## 📊 数据解析逻辑

### 智能格式识别
系统会自动尝试以下解析方式：
1. 首先尝试按行解析（格式1）
2. 然后尝试逗号分隔解析（格式2）
3. 最后尝试空格分隔解析（格式3）
4. 如果都失败，则抛出格式错误

### 数据验证
- 确保每期数据都是5位数字
- 过滤无效数据行
- 统计有效数据期数

## 🔄 更新的功能

### 1. 数据加载方法
```python
def load_from_hashffc(self, file_path):
    """从HASHFFC.txt加载数据"""
    # 支持多种格式的智能解析
    
def parse_hashffc_format1(self, content):
    """解析格式1: 每行一个5位数字"""
    
def parse_hashffc_format2(self, content):
    """解析格式2: 逗号分隔的5位数字"""
    
def parse_hashffc_format3(self, content):
    """解析格式3: 空格分隔的单个数字"""
```

### 2. 状态显示增强
- 状态栏明确显示当前使用的数据源
- 显示加载的数据期数
- 刷新时保持数据源信息

### 3. 错误处理机制
- 文件不存在时自动回退到备用数据源
- 格式无法识别时提供详细错误信息
- 保证系统在任何情况下都能正常运行

## 🚀 使用方法

### 1. 准备数据文件
将HASHFFC.txt文件放置在以下任一位置：
- `D:\辉达挂机软件 - 副本\OpenCode\HASHFFC.txt` (推荐)
- 当前程序目录下的 `HASHFFC.txt`

### 2. 启动程序
```bash
# 使用启动脚本
python run_lottery_system.py

# 或直接运行
python lottery_strategy_selector.py
```

### 3. 验证数据源
- 查看状态栏显示的数据源信息
- 确认加载的数据期数
- 观察策略统计是否基于真实数据

## 🧪 测试验证

### 运行测试脚本
```bash
python test_hashffc_data.py
```

测试内容包括：
- 不同数据格式的解析测试
- 数据加载功能测试
- 策略统计计算测试
- 错误处理测试

### 测试结果示例
```
测试格式1: 每行一个5位数字
   解析结果: True
   解析数据: ['12345', '67890', '13579']

测试格式2: 逗号分隔
   解析结果: True
   解析数据: ['12345', '67890', '13579']

测试格式3: 空格分隔的单个数字
   解析结果: True
   解析数据: ['12345', '67890', '13579']
```

## 📈 策略统计示例

基于HASHFFC.txt数据的策略统计：
```
各策略连续未出现次数:
和值: 3期
跨度: 0期
奇偶比: 16期
大小比: 3期
连号: 11期
重号: 0期
质合比: 0期
012路: 3期
形态: 1期
位置和: 3期
AC值: 5期
冷热度: 6期
```

## 🔍 状态栏信息

### 数据源显示格式
- `数据源: HASHFFC.txt | 已加载 100 期历史数据`
- `数据源: HASHFFC.txt (本地) | 已加载 50 期历史数据`
- `数据源: results.csv | 已加载 30 期历史数据`
- `数据源: 演示数据 | 已加载 50 期历史数据`

### 刷新后状态
- `数据源: HASHFFC.txt | 已刷新 100 期数据`

## ⚠️ 注意事项

### 1. 文件路径
- 确保HASHFFC.txt文件路径正确
- 支持中文路径和文件名
- 建议使用绝对路径避免路径问题

### 2. 数据格式
- 确保数据为5位数字格式
- 避免包含非数字字符
- 每期数据应该完整（5位数字）

### 3. 文件编码
- 支持UTF-8编码
- 兼容GBK等中文编码
- 自动处理BOM标记

### 4. 性能考虑
- 大文件加载可能需要时间
- 建议数据文件不超过10MB
- 系统会自动优化内存使用

## 🔧 故障排除

### 问题1: 文件找不到
**现象**: 显示"数据源: 演示数据"
**解决**: 
1. 检查文件路径是否正确
2. 确认文件是否存在
3. 检查文件权限

### 问题2: 数据格式错误
**现象**: 提示"无法识别HASHFFC.txt文件格式"
**解决**:
1. 检查数据格式是否符合要求
2. 确认每行数据完整性
3. 移除非数字字符

### 问题3: 数据期数为0
**现象**: 显示"已加载 0 期历史数据"
**解决**:
1. 检查文件内容是否为空
2. 确认数据格式正确
3. 查看错误日志信息

## 📝 更新日志

### v1.1.0 (2024-07-31)
- ✅ 添加HASHFFC.txt数据源支持
- ✅ 实现多格式智能解析
- ✅ 增强状态栏信息显示
- ✅ 完善错误处理机制
- ✅ 添加数据源优先级系统
- ✅ 创建专用测试脚本

### 兼容性
- ✅ 保持与原有功能完全兼容
- ✅ 支持所有原有数据源
- ✅ 策略算法保持不变
- ✅ 界面布局无变化

---

**数据源集成完成** ✅  
现在系统优先使用HASHFFC.txt文件，并基于真实历史数据计算策略统计信息。
