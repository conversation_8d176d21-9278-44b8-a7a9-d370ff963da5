#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试5星彩票杀码策略选择界面
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import wx
    from lottery_strategy_selector import LotteryStrategySelector, LotteryStrategyApp
    
    def test_strategy_selector():
        """测试策略选择器"""
        print("启动5星彩票杀码策略选择器...")
        
        app = LotteryStrategyApp()
        app.MainLoop()
        
    if __name__ == '__main__':
        test_strategy_selector()
        
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装wxPython: pip install wxpython")
except Exception as e:
    print(f"运行错误: {e}")
    import traceback
    traceback.print_exc()
