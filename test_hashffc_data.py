#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试HASHFFC.txt数据源加载功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_hashffc_parsing():
    """测试HASHFFC.txt文件解析功能"""
    print("=" * 50)
    print("测试HASHFFC.txt数据源加载功能")
    print("=" * 50)
    
    try:
        from lottery_strategy_selector import LotteryStrategySelector
        
        # 创建策略选择器实例（不显示界面）
        import wx
        app = wx.App(False)  # 不显示主循环
        
        selector = LotteryStrategySelector()
        
        # 测试数据加载
        print("\n1. 测试数据加载...")
        selector.load_historical_data()
        
        print(f"   加载的数据期数: {len(selector.historical_data)}")
        if selector.historical_data:
            print(f"   最新5期数据: {selector.historical_data[-5:]}")
            print(f"   最早5期数据: {selector.historical_data[:5]}")
        
        # 测试策略计算
        print("\n2. 测试策略统计计算...")
        selector.calculate_strategy_stats()
        
        print("   各策略连续未出现次数:")
        for strategy, count in selector.strategy_stats.items():
            print(f"   {strategy}: {count}期")
        
        # 测试数据源识别
        print(f"\n3. 当前状态: {selector.status_text.GetLabel()}")
        
        print("\n✓ 测试完成，所有功能正常")
        
        app.Destroy()
        return True
        
    except Exception as e:
        print(f"\n✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_formats():
    """测试不同数据格式的解析"""
    print("\n" + "=" * 50)
    print("测试不同数据格式解析")
    print("=" * 50)
    
    try:
        from lottery_strategy_selector import LotteryStrategySelector
        import wx
        
        app = wx.App(False)
        selector = LotteryStrategySelector()
        
        # 测试格式1: 每行一个5位数字
        print("\n测试格式1: 每行一个5位数字")
        test_content1 = "12345\n67890\n13579\n24680\n11223"
        result1 = selector.parse_hashffc_format1(test_content1)
        print(f"   解析结果: {result1}")
        print(f"   解析数据: {selector.historical_data[:3] if selector.historical_data else '无'}")
        
        # 重置数据
        selector.historical_data = []
        
        # 测试格式2: 逗号分隔
        print("\n测试格式2: 逗号分隔")
        test_content2 = "12345,67890,13579,24680,11223"
        result2 = selector.parse_hashffc_format2(test_content2)
        print(f"   解析结果: {result2}")
        print(f"   解析数据: {selector.historical_data[:3] if selector.historical_data else '无'}")
        
        # 重置数据
        selector.historical_data = []
        
        # 测试格式3: 空格分隔的单个数字
        print("\n测试格式3: 空格分隔的单个数字")
        test_content3 = "1 2 3 4 5 6 7 8 9 0 1 3 5 7 9 2 4 6 8 0 1 1 2 2 3"
        result3 = selector.parse_hashffc_format3(test_content3)
        print(f"   解析结果: {result3}")
        print(f"   解析数据: {selector.historical_data[:3] if selector.historical_data else '无'}")
        
        app.Destroy()
        return True
        
    except Exception as e:
        print(f"\n✗ 格式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_files():
    """创建测试文件"""
    print("\n创建测试文件...")
    
    # 创建不同格式的测试文件
    test_files = {
        'test_format1.txt': "12345\n67890\n13579\n24680\n11223\n98765\n56789\n34567\n78901\n45678",
        'test_format2.txt': "12345,67890,13579,24680,11223,98765,56789,34567,78901,45678",
        'test_format3.txt': "1 2 3 4 5 6 7 8 9 0 1 3 5 7 9 2 4 6 8 0 1 1 2 2 3 9 8 7 6 5 5 6 7 8 9 3 4 5 6 7 7 8 9 0 1 4 5 6 7 8"
    }
    
    for filename, content in test_files.items():
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"   ✓ 创建 {filename}")
    
    print("   ✓ 测试文件创建完成")

def main():
    """主函数"""
    try:
        # 创建测试文件
        create_test_files()
        
        # 测试数据格式解析
        if not test_data_formats():
            return False
        
        # 测试完整功能
        if not test_hashffc_parsing():
            return False
        
        print("\n" + "=" * 50)
        print("所有测试通过！")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"\n测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    try:
        success = main()
        if not success:
            input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
    except Exception as e:
        print(f"\n程序异常: {e}")
        input("按回车键退出...")
