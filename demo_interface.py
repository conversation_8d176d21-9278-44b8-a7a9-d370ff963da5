#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
5星彩票杀码策略选择器演示界面
简化版本，用于展示界面效果
"""

import wx
import random

class DemoStrategySelector(wx.Frame):
    def __init__(self):
        super().__init__(None, title="5星彩票杀码策略选择器 - 演示版", size=(800, 600))
        
        self.selected_strategies = set()
        
        # 策略定义和模拟数据
        self.strategies = {
            '和值': random.randint(1, 15),
            '跨度': random.randint(1, 12),
            '奇偶比': random.randint(1, 8),
            '大小比': random.randint(1, 10),
            '连号': random.randint(1, 6),
            '重号': random.randint(1, 9),
            '质合比': random.randint(1, 7),
            '012路': random.randint(1, 11),
            '形态': random.randint(1, 5),
            '位置和': random.randint(1, 13),
            'AC值': random.randint(1, 8),
            '冷热度': random.randint(1, 14)
        }
        
        self.init_ui()
        self.Center()
        
    def init_ui(self):
        """初始化用户界面"""
        panel = wx.Panel(self)
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 标题
        title = wx.StaticText(panel, label="5星彩票杀码策略选择器")
        title_font = wx.Font(16, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD)
        title.SetFont(title_font)
        main_sizer.Add(title, 0, wx.ALL | wx.CENTER, 15)
        
        # 说明文字
        desc = wx.StaticText(panel, label="选择杀码策略，紫红色数字表示该特征连续多少期没有出现")
        main_sizer.Add(desc, 0, wx.ALL | wx.CENTER, 5)
        
        # 策略按钮区域
        strategy_box = wx.StaticBox(panel, label="策略选择")
        strategy_sizer = wx.StaticBoxSizer(strategy_box, wx.VERTICAL)
        
        # 创建策略按钮网格 (4列3行)
        grid_sizer = wx.GridSizer(3, 4, 15, 15)
        
        self.strategy_buttons = {}
        self.strategy_labels = {}
        
        for strategy_name, count in self.strategies.items():
            # 创建按钮容器
            btn_panel = wx.Panel(panel)
            btn_sizer = wx.BoxSizer(wx.HORIZONTAL)
            
            # 策略按钮
            btn = wx.ToggleButton(btn_panel, label=strategy_name, size=(80, 35))
            btn.Bind(wx.EVT_TOGGLEBUTTON, lambda evt, name=strategy_name: self.on_strategy_toggle(evt, name))
            self.strategy_buttons[strategy_name] = btn
            
            # 统计数字标签
            stat_label = wx.StaticText(btn_panel, label=str(count), size=(25, -1))
            stat_label.SetForegroundColour(wx.Colour(128, 0, 128))  # 紫红色
            stat_font = wx.Font(12, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD)
            stat_label.SetFont(stat_font)
            self.strategy_labels[strategy_name] = stat_label
            
            btn_sizer.Add(btn, 0, wx.ALL, 2)
            btn_sizer.Add(stat_label, 0, wx.ALL | wx.ALIGN_CENTER_VERTICAL, 2)
            btn_panel.SetSizer(btn_sizer)
            
            grid_sizer.Add(btn_panel, 0, wx.EXPAND)
        
        strategy_sizer.Add(grid_sizer, 0, wx.ALL | wx.EXPAND, 15)
        main_sizer.Add(strategy_sizer, 0, wx.ALL | wx.EXPAND, 15)
        
        # 选中策略显示区域
        selected_box = wx.StaticBox(panel, label="已选择的策略")
        selected_sizer = wx.StaticBoxSizer(selected_box, wx.VERTICAL)
        
        self.selected_text = wx.TextCtrl(panel, style=wx.TE_MULTILINE | wx.TE_READONLY, size=(-1, 120))
        self.selected_text.SetValue("未选择任何策略")
        selected_sizer.Add(self.selected_text, 1, wx.ALL | wx.EXPAND, 5)
        
        main_sizer.Add(selected_sizer, 0, wx.ALL | wx.EXPAND, 15)
        
        # 操作按钮
        btn_sizer = wx.BoxSizer(wx.HORIZONTAL)
        
        clear_btn = wx.Button(panel, label="清空选择", size=(100, 35))
        clear_btn.Bind(wx.EVT_BUTTON, self.on_clear_selection)
        
        apply_btn = wx.Button(panel, label="应用策略", size=(100, 35))
        apply_btn.Bind(wx.EVT_BUTTON, self.on_apply_strategies)
        
        refresh_btn = wx.Button(panel, label="刷新数据", size=(100, 35))
        refresh_btn.Bind(wx.EVT_BUTTON, self.on_refresh_data)
        
        btn_sizer.Add(clear_btn, 0, wx.ALL, 5)
        btn_sizer.Add(apply_btn, 0, wx.ALL, 5)
        btn_sizer.Add(refresh_btn, 0, wx.ALL, 5)
        btn_sizer.AddStretchSpacer()
        
        main_sizer.Add(btn_sizer, 0, wx.ALL | wx.EXPAND, 15)
        
        # 状态栏
        self.status_text = wx.StaticText(panel, label="演示模式 - 数据为随机生成")
        main_sizer.Add(self.status_text, 0, wx.ALL | wx.EXPAND, 5)
        
        panel.SetSizer(main_sizer)
        
    def on_strategy_toggle(self, event, strategy_name):
        """策略按钮切换事件"""
        button = event.GetEventObject()
        if button.GetValue():
            self.selected_strategies.add(strategy_name)
        else:
            self.selected_strategies.discard(strategy_name)
            
        self.update_selected_display()
        
    def update_selected_display(self):
        """更新已选择策略的显示"""
        if not self.selected_strategies:
            self.selected_text.SetValue("未选择任何策略")
            return
            
        text_lines = [f"已选择 {len(self.selected_strategies)} 个策略:"]
        for strategy_name in sorted(self.selected_strategies):
            count = self.strategies[strategy_name]
            text_lines.append(f"• {strategy_name}: {count}期未出现")
            
        self.selected_text.SetValue("\n".join(text_lines))
        
    def on_clear_selection(self, event):
        """清空选择事件"""
        self.selected_strategies.clear()
        
        # 重置所有按钮状态
        for button in self.strategy_buttons.values():
            button.SetValue(False)
            
        self.update_selected_display()
        self.status_text.SetLabel("已清空所有选择")
        
    def on_apply_strategies(self, event):
        """应用策略事件"""
        if not self.selected_strategies:
            wx.MessageBox("请先选择至少一个策略", "提示", wx.OK | wx.ICON_INFORMATION)
            return
            
        strategy_list = list(self.selected_strategies)
        message = f"已选择 {len(strategy_list)} 个策略:\n\n"
        
        for strategy_name in sorted(strategy_list):
            count = self.strategies[strategy_name]
            message += f"• {strategy_name}: {count}期未出现\n"
            
        message += "\n这些策略将用于杀码筛选。"
        
        wx.MessageBox(message, "策略应用", wx.OK | wx.ICON_INFORMATION)
        self.status_text.SetLabel(f"已应用 {len(strategy_list)} 个策略")
        
    def on_refresh_data(self, event):
        """刷新数据事件"""
        # 重新生成随机数据
        for strategy_name in self.strategies:
            new_count = random.randint(1, 15)
            self.strategies[strategy_name] = new_count
            self.strategy_labels[strategy_name].SetLabel(str(new_count))
            
        self.update_selected_display()
        self.status_text.SetLabel("数据已刷新")


class DemoApp(wx.App):
    def OnInit(self):
        frame = DemoStrategySelector()
        frame.Show()
        return True


def main():
    """主函数"""
    print("启动5星彩票杀码策略选择器演示版...")
    app = DemoApp()
    app.MainLoop()


if __name__ == '__main__':
    main()
