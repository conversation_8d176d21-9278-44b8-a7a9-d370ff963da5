# 5星彩票杀码策略选择器

## 功能概述

这是一个专为5星彩票设计的杀码策略选择界面，基于历史数据分析各种策略特征的连续未出现次数，帮助用户选择最优的杀码策略。

## 主要特性

### 1. 策略分析
- **和值分析**: 分析5位数字和值的连续未出现次数
- **跨度分析**: 分析最大数字与最小数字差值的连续未出现次数
- **奇偶比分析**: 分析奇数与偶数比例的连续未出现次数
- **大小比分析**: 分析大数(5-9)与小数(0-4)比例的连续未出现次数
- **连号分析**: 分析连续数字出现情况的连续未出现次数
- **重号分析**: 分析重复数字出现情况的连续未出现次数
- **质合比分析**: 分析质数与合数比例的连续未出现次数
- **012路分析**: 分析数字除以3的余数分布的连续未出现次数
- **形态分析**: 分析数字排列形态的连续未出现次数
- **位置和分析**: 分析各位置数字和值的连续未出现次数
- **AC值分析**: 分析数字复杂度指标的连续未出现次数
- **冷热度分析**: 分析数字出现频率的连续未出现次数

### 2. 界面设计
- **紧凑布局**: 最大化空间利用率，4列3行按钮布局
- **直观显示**: 每个策略按钮右侧显示紫红色数字，表示连续未出现次数
- **多选支持**: 支持同时选择多个策略进行组合分析
- **实时更新**: 数据变化时自动更新统计信息

### 3. 数据源支持
- **results.csv**: 优先使用当前目录的results.csv文件
- **总历史数据.txt**: 备用数据源，支持和值数据格式
- **演示数据**: 当没有数据文件时自动生成演示数据

## 文件结构

```
├── lottery_strategy_selector.py    # 策略选择器主程序
├── integrated_lottery_app.py       # 集成系统主界面
├── test_strategy_selector.py       # 测试脚本
├── run_lottery_system.py          # 启动脚本
├── README_策略选择器.md            # 说明文档
├── results.csv                    # 历史数据文件(可选)
└── 总历史数据.txt                  # 备用数据文件(可选)
```

## 安装和运行

### 1. 环境要求
- Python 3.6 或更高版本
- wxPython 库

### 2. 自动安装和运行
```bash
python run_lottery_system.py
```

启动脚本会自动：
- 检查Python版本
- 检查并安装依赖包
- 创建演示数据(如果需要)
- 提供多种启动模式选择

### 3. 手动安装依赖
```bash
pip install wxpython -i https://pypi.tuna.tsinghua.edu.cn/simple
```

### 4. 直接运行
```bash
# 运行策略选择器
python lottery_strategy_selector.py

# 运行集成系统
python integrated_lottery_app.py

# 运行测试
python test_strategy_selector.py
```

## 使用说明

### 1. 启动程序
运行启动脚本，选择启动模式：
- 模式1: 策略选择器(单独运行)
- 模式2: 集成系统(推荐)
- 模式3: 测试模式

### 2. 策略选择
- 点击策略按钮进行选择/取消选择
- 观察按钮右侧的紫红色数字，数字越大表示该特征越久没有出现
- 支持多选，可以组合多个策略

### 3. 查看选择结果
- 在"已选择的策略"区域查看当前选择
- 每个策略显示连续未出现次数和详细描述

### 4. 应用策略
- 点击"应用策略"按钮确认选择
- 策略将用于后续的号码筛选和杀码操作

### 5. 数据管理
- 点击"刷新数据"更新统计信息
- 点击"清空选择"重置所有选择

## 策略说明

### 连续未出现次数的意义
- **数字越大**: 表示该特征越久没有出现，可能即将出现
- **数字越小**: 表示该特征最近出现过，短期内再次出现概率较低
- **选择策略**: 根据个人经验和分析选择合适的策略组合

### 推荐策略组合
1. **保守型**: 和值 + 跨度 + 奇偶比
2. **激进型**: 连号 + 重号 + 形态
3. **平衡型**: 和值 + 奇偶比 + 大小比 + 冷热度

## 技术特点

### 1. 界面设计
- 使用wxPython框架，界面美观稳定
- 紧凑布局，最大化空间利用率
- 紫红色数字醒目显示统计信息

### 2. 数据处理
- 支持多种数据源格式
- 实时计算各策略特征
- 高效的连续未出现次数算法

### 3. 扩展性
- 模块化设计，易于添加新策略
- 可集成到现有彩票分析系统
- 支持自定义策略参数

## 注意事项

1. **数据准确性**: 确保历史数据文件格式正确
2. **策略选择**: 根据实际情况选择合适的策略组合
3. **风险提示**: 彩票具有随机性，策略仅供参考
4. **定期更新**: 建议定期更新历史数据以保持分析准确性

## 更新日志

### v1.0.0 (2024-07-31)
- 初始版本发布
- 实现12种杀码策略分析
- 支持多数据源和演示模式
- 集成系统界面
- 自动依赖检查和安装

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue到项目仓库
- 发送邮件到开发者邮箱

---

**免责声明**: 本软件仅供学习和研究使用，不构成任何投资建议。彩票具有随机性，请理性参与。
