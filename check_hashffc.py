#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查HASHFFC.txt文件格式
"""

import os

def check_hashffc_file():
    """检查HASHFFC.txt文件"""
    file_path = r"D:\辉达挂机软件 - 副本\OpenCode\HASHFFC.txt"
    
    print(f"检查文件: {file_path}")
    print("=" * 60)
    
    if not os.path.exists(file_path):
        print("文件不存在")
        return False
    
    try:
        # 尝试不同编码读取文件
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16']
        
        for encoding in encodings:
            try:
                print(f"\n尝试编码: {encoding}")
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                    
                print(f"文件大小: {len(content)} 字符")
                print(f"前100个字符: {repr(content[:100])}")
                
                # 分析内容格式
                lines = content.split('\n')
                print(f"总行数: {len(lines)}")
                print(f"前5行: {lines[:5]}")
                
                # 检查是否包含5位数字
                five_digit_count = 0
                for line in lines[:20]:  # 检查前20行
                    line = line.strip()
                    if len(line) == 5 and line.isdigit():
                        five_digit_count += 1
                
                print(f"前20行中5位数字行数: {five_digit_count}")
                
                # 检查逗号分隔格式
                comma_separated = content.count(',')
                print(f"逗号数量: {comma_separated}")
                
                # 检查空格分隔格式
                space_separated = len(content.split())
                print(f"空格分隔的词数: {space_separated}")
                
                return True
                
            except UnicodeDecodeError:
                print(f"编码 {encoding} 失败")
                continue
            except Exception as e:
                print(f"读取失败: {e}")
                continue
        
        print("所有编码都失败")
        return False
        
    except Exception as e:
        print(f"检查文件时出错: {e}")
        return False

def main():
    """主函数"""
    print("HASHFFC.txt文件格式检查工具")
    print("=" * 60)
    
    success = check_hashffc_file()
    
    if not success:
        print("\n检查本地HASHFFC.txt文件:")
        local_path = "HASHFFC.txt"
        if os.path.exists(local_path):
            with open(local_path, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"本地文件内容: {content[:200]}")
        else:
            print("本地文件不存在")

if __name__ == '__main__':
    main()
    input("\n按回车键退出...")
