#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
5星彩票分析系统启动脚本
检查依赖并启动主程序
"""

import sys
import os
import subprocess

def check_dependencies():
    """检查依赖包"""
    required_packages = ['wx']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} 未安装")
    
    return missing_packages

def install_packages(packages):
    """安装缺失的包"""
    if not packages:
        return True
        
    print("\n正在安装缺失的依赖包...")
    
    # 使用国内镜像源
    pip_sources = [
        "https://pypi.tuna.tsinghua.edu.cn/simple",
        "https://mirrors.aliyun.com/pypi/simple",
        "https://pypi.douban.com/simple"
    ]
    
    for package in packages:
        if package == 'wx':
            package_name = 'wxpython'
        else:
            package_name = package
            
        for source in pip_sources:
            try:
                print(f"尝试从 {source} 安装 {package_name}...")
                result = subprocess.run([
                    sys.executable, '-m', 'pip', 'install', 
                    package_name, '-i', source
                ], capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    print(f"✓ {package_name} 安装成功")
                    break
                else:
                    print(f"✗ 安装失败: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                print(f"✗ 安装超时")
                continue
            except Exception as e:
                print(f"✗ 安装出错: {e}")
                continue
        else:
            print(f"✗ {package_name} 安装失败，请手动安装")
            return False
    
    return True

def create_demo_data():
    """创建演示数据文件"""
    # 检查是否存在指定的HASHFFC.txt文件
    hashffc_path = r"D:\辉达挂机软件 - 副本\OpenCode\HASHFFC.txt"
    if os.path.exists(hashffc_path):
        try:
            # 验证文件是否可读且有内容
            with open(hashffc_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if content:
                    print(f"✓ 发现指定数据源: {hashffc_path}")
                    return
                else:
                    print(f"⚠ 指定数据源为空: {hashffc_path}")
        except Exception as e:
            print(f"⚠ 指定数据源读取失败: {hashffc_path} - {e}")

    # 检查本地HASHFFC.txt文件
    if os.path.exists('HASHFFC.txt'):
        print("✓ 发现本地HASHFFC.txt数据文件")
        return

    # 创建本地HASHFFC.txt演示文件
    if not os.path.exists('HASHFFC.txt'):
        print("创建HASHFFC.txt演示数据文件...")

        import random

        with open('HASHFFC.txt', 'w', encoding='utf-8') as f:
            for i in range(100):
                number = ''.join([str(random.randint(0, 9)) for _ in range(5)])
                f.write(number + '\n')

        print("✓ HASHFFC.txt演示数据文件创建完成")

    # 备用：创建results.csv文件
    if not os.path.exists('results.csv'):
        print("创建results.csv备用数据文件...")

        import random
        import csv

        with open('results.csv', 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['number', 'period', 'date'])

            for i in range(50):
                number = ''.join([str(random.randint(0, 9)) for _ in range(5)])
                period = f"2024{i+1:03d}"
                date = f"2024-01-{(i%30)+1:02d}"
                writer.writerow([number, period, date])

        print("✓ results.csv备用数据文件创建完成")

def main():
    """主函数"""
    print("=" * 50)
    print("5星彩票分析系统启动器")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("✗ 需要Python 3.6或更高版本")
        return False
    
    print(f"✓ Python版本: {sys.version}")
    
    # 检查依赖
    print("\n检查依赖包...")
    missing = check_dependencies()
    
    if missing:
        print(f"\n发现缺失的依赖包: {missing}")
        install_choice = input("是否自动安装? (y/n): ").lower().strip()
        
        if install_choice == 'y':
            if not install_packages(missing):
                print("\n依赖安装失败，请手动安装后重试")
                return False
        else:
            print("请手动安装依赖包后重试")
            return False
    
    # 创建演示数据
    create_demo_data()
    
    # 启动主程序
    print("\n启动5星彩票分析系统...")
    try:
        # 选择启动模式
        print("\n请选择启动模式:")
        print("1. 策略选择器 (单独运行)")
        print("2. 集成系统 (推荐)")
        print("3. 测试模式")
        
        choice = input("请输入选择 (1-3): ").strip()
        
        if choice == '1':
            from lottery_strategy_selector import main as strategy_main
            strategy_main()
        elif choice == '2':
            from integrated_lottery_app import main as integrated_main
            integrated_main()
        elif choice == '3':
            from test_strategy_selector import test_strategy_selector
            test_strategy_selector()
        else:
            print("无效选择，启动集成系统...")
            from integrated_lottery_app import main as integrated_main
            integrated_main()
            
    except ImportError as e:
        print(f"✗ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"✗ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == '__main__':
    try:
        success = main()
        if not success:
            input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n用户中断，程序退出")
    except Exception as e:
        print(f"\n程序异常: {e}")
        input("按回车键退出...")
