import wx
import os
import re
import time
from itertools import product

class FilterResultComponent(wx.Panel):
    """筛选结果显示组件 - 独立的UI组件，与kill_number_display_wx.py保持一致的接口规范"""

    def __init__(self, parent):
        super().__init__(parent)

        # 组件数据存储
        self.filter_result_count = 0
        self.final_filtered_numbers = []
        self.filter_process_log = []

        # 与kill_number_display_wx.py保持一致的属性名
        self.numbers_text = None  # 主要筛选结果控件，与kill_number_display_wx.py一致
        self.numbers_per_row = 10  # 每行显示的号码数量，与kill_number_display_wx.py一致

        # 筛选策略配置
        self.filter_strategies = {
            'sum_filter': {'enabled': False, 'count': 13, 'name': '和值统计筛选'},
            'size_combo_filter': {'enabled': False, 'count': 5, 'name': '大小组合筛选', 'mode': 'top_n'},
            'position_digit_filter': {'enabled': False, 'count': 1, 'name': '位置数字筛选', 'mode': 'top_n'},
            'parity_combo_filter': {'enabled': False, 'count': 5, 'name': '奇偶组合筛选', 'mode': 'top_n'},
            'digit_absence_filter': {'enabled': False, 'count': 10, 'name': '数字连续最多', 'prediction_count': 1, 'predicted_digits': [], 'predicted_digit': None, 'hit_rate': 0.0, 'total_predictions': 0, 'correct_predictions': 0},
            'latest_absence_filter': {'enabled': False, 'count': 999, 'name': '未出现最少分析', 'prediction_count': 1, 'predicted_digits': [], 'hit_rate': 0.0, 'total_predictions': 0, 'correct_predictions': 0},
            'kill_previous_pattern_filter': {'enabled': False, 'count': 50, 'pattern_type': 'sum', 'name': '杀上期形态', 'consecutive_count': 0, 'current_pattern': None, 'pattern_history': [], 'statistics_text': '', 'hit_rate': 0.0, 'total_predictions': 0, 'correct_predictions': 0, 'filter_threshold': 10, 'enable_filtering': False, 'excluded_pattern_value': None}
        }

        # 筛选状态管理
        self.strategy_changed = False
        self.last_filter_time = None
        self.auto_filter_timer = None

        # 预测记录管理
        self.prediction_records = []  # 存储预测记录
        self.prediction_file = "prediction_records.json"  # 预测记录文件
        self.last_checked_number = None  # 记录上次检查的开奖号码，避免重复检查
        self.load_prediction_records()  # 加载历史预测记录

        # 初始化组件UI
        self.init_component_ui()

    def init_component_ui(self):
        """初始化筛选结果组件的用户界面，扁平化独立组件设计"""
        # 创建组件的主布局 - 直接使用BoxSizer，移除StaticBoxSizer容器
        component_sizer = wx.BoxSizer(wx.VERTICAL)

        # 添加筛选策略选择面板（固定宽度，不随窗口变化）
        strategy_panel = self.create_strategy_panel()
        component_sizer.Add(strategy_panel, 0, wx.ALL | wx.ALIGN_LEFT, 3)

        # 预测控制按钮已移动到策略面板内部

        # 筛选过程和结果显示区域（三列布局）
        filter_content_sizer = wx.BoxSizer(wx.HORIZONTAL)

        # 左侧：筛选过程文本框（固定宽度）
        self.filter_process_text = wx.TextCtrl(self, style=wx.TE_MULTILINE | wx.TE_READONLY, size=(315, 220))
        filter_content_sizer.Add(self.filter_process_text, 0, wx.ALL, 3)

        # 右侧：筛选结果控件（固定宽度，与上方统计窗口右边界对齐）
        self.numbers_text = wx.TextCtrl(self, style=wx.TE_MULTILINE | wx.HSCROLL | wx.VSCROLL, size=(500, 220))
        # 设置与kill_number_display_wx.py相同的字体
        numbers_font = wx.Font(10, wx.FONTFAMILY_TELETYPE, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL)
        self.numbers_text.SetFont(numbers_font)
        filter_content_sizer.Add(self.numbers_text, 0, wx.ALL, 3)

        # 保持向后兼容性的别名
        self.filter_result_text = self.numbers_text

        # 直接添加到主布局，无需中间容器
        component_sizer.Add(filter_content_sizer, 1, wx.EXPAND | wx.ALL, 3)

        # 控制按钮和状态指示区域
        button_sizer = wx.BoxSizer(wx.HORIZONTAL)

        # 筛选状态指示器
        self.status_label = wx.StaticText(self, label="筛选状态: 准备就绪")
        self.status_label.SetForegroundColour(wx.Colour(0, 128, 0))  # 绿色
        button_sizer.Add(self.status_label, 0, wx.ALL | wx.ALIGN_CENTER_VERTICAL, 5)

        # 进度条组件已禁用 - 保持筛选功能但移除可视化反馈
        # self.progress_gauge = wx.Gauge(self, range=100, size=(150, 20))
        # self.progress_gauge.Hide()
        # 不添加到布局中，避免占用空间
        # button_sizer.Add(self.progress_gauge, 0, wx.ALL | wx.ALIGN_CENTER_VERTICAL, 5)

        # 添加大量弹性空间和固定空间，强制将清空结果按钮推到最右边
        button_sizer.AddStretchSpacer(10)  # 增加权重
        button_sizer.AddSpacer(200)        # 添加固定空间

        self.clear_filter_btn = wx.Button(self, label="清空结果", size=(80, 28))
        self.clear_filter_btn.Bind(wx.EVT_BUTTON, self.on_clear_filter)
        button_sizer.Add(self.clear_filter_btn, 0, wx.ALL, 2)

        # 直接添加到主布局，使用EXPAND确保按钮行占满宽度
        component_sizer.Add(button_sizer, 0, wx.ALL | wx.EXPAND, 1)

        # 设置组件主布局
        self.SetSizer(component_sizer)

    # 预测面板已移动到主程序右侧，此处不再需要

    def create_strategy_panel(self):
        """创建筛选策略选择面板（1行4列水平布局，固定宽度）"""
        # 创建策略面板的静态框
        strategy_box = wx.StaticBoxSizer(wx.VERTICAL, self, "筛选策略选择")

        # 使用水平布局的主容器，设置固定宽度
        main_strategy_sizer = wx.BoxSizer(wx.HORIZONTAL)

        # 设置策略面板的固定宽度（7个面板，考虑实际宽度）
        self.strategy_panel_width = 1350  # 固定宽度支持7个策略，调整以适应新增的杀上期形态策略

        # 存储策略控件的字典
        self.strategy_controls = {}

        # 获取策略列表，确保顺序
        strategy_keys = ['sum_filter', 'size_combo_filter', 'position_digit_filter', 'parity_combo_filter', 'digit_absence_filter', 'latest_absence_filter', 'kill_previous_pattern_filter']

        print(f"🔧 创建1行7列策略布局，策略顺序: {strategy_keys}")

        # 创建所有策略组件，水平排列
        for i, strategy_key in enumerate(strategy_keys):
            if strategy_key in self.filter_strategies:
                strategy_info = self.filter_strategies[strategy_key]
                strategy_panel = self.create_single_strategy_panel(strategy_key, strategy_info)

                # 添加到水平布局，使用固定宽度，不随窗口变化
                main_strategy_sizer.Add(strategy_panel, 0, wx.ALL, 3)
                print(f"   添加策略 {i+1}: {strategy_info['name']}")

        # 设置策略面板容器，固定宽度不变化
        strategy_box.Add(main_strategy_sizer, 0, wx.ALL | wx.ALIGN_LEFT, 5)

        # 预测控制按钮已移动到主程序布局中

        # 设置整个策略面板的固定宽度，不允许扩展
        strategy_box.SetMinSize((self.strategy_panel_width, -1))
        # StaticBoxSizer没有SetMaxSize方法，通过其他方式限制宽度

        return strategy_box

    def create_single_strategy_panel(self, strategy_key, strategy_info):
        """创建单个策略的面板（简洁版，无统计信息）"""
        # 创建策略容器
        strategy_panel = wx.Panel(self)
        strategy_sizer = wx.BoxSizer(wx.VERTICAL)  # 改为垂直布局以容纳更多控件

        # 设置面板固定尺寸，不随窗口变化
        # 使用固定宽度值，确保策略面板不会随窗口大小改变
        if strategy_key == 'sum_filter':
            strategy_panel.SetMinSize((90, 50))  # 和值统计筛选固定宽度
            strategy_panel.SetMaxSize((90, 50))  # 限制最大宽度
        elif strategy_key in ['size_combo_filter', 'parity_combo_filter', 'position_digit_filter']:
            strategy_panel.SetMinSize((150, 60))  # 支持模式选择的策略固定宽度，再增加10px
            strategy_panel.SetMaxSize((150, 60))  # 限制最大宽度
        elif strategy_key == 'digit_absence_filter':
            strategy_panel.SetMinSize((120, 70))  # 数字连续最多策略，需要额外空间放置预测数量控件
            strategy_panel.SetMaxSize((120, 70))  # 限制最大宽度
        elif strategy_key == 'latest_absence_filter':
            strategy_panel.SetMinSize((120, 70))  # 未出现最少分析策略，需要额外空间放置预测数量控件
            strategy_panel.SetMaxSize((120, 70))  # 限制最大宽度
        elif strategy_key == 'kill_previous_pattern_filter':
            strategy_panel.SetMinSize((130, 110))  # 杀上期形态策略，需要额外空间放置形态类型选择控件和筛选阈值控件
            strategy_panel.SetMaxSize((130, 110))  # 限制最大宽度
        else:
            strategy_panel.SetMinSize((115, 35))  # 普通策略固定宽度
            strategy_panel.SetMaxSize((115, 35))  # 限制最大宽度

        # 第一行：复选框（策略名称）
        top_sizer = wx.BoxSizer(wx.HORIZONTAL)
        checkbox = wx.CheckBox(strategy_panel, label=strategy_info['name'])
        checkbox.SetValue(strategy_info['enabled'])
        checkbox.Bind(wx.EVT_CHECKBOX, lambda evt, key=strategy_key: self.on_strategy_toggle(evt, key))

        # 设置复选框字体
        font = checkbox.GetFont()
        font.SetPointSize(8)
        checkbox.SetFont(font)

        top_sizer.Add(checkbox, 1, wx.ALIGN_CENTER_VERTICAL)
        strategy_sizer.Add(top_sizer, 0, wx.EXPAND | wx.BOTTOM, 3)

        # 第二行：数值控制和模式选择
        bottom_sizer = wx.BoxSizer(wx.HORIZONTAL)

        # 为分析策略创建特殊控件
        if strategy_key in ['digit_absence_filter', 'latest_absence_filter']:
            # 根据策略类型设置标签
            if strategy_key == 'digit_absence_filter':
                label_text = "最近"
            else:  # latest_absence_filter
                label_text = "范围"

            # 标签
            count_label = wx.StaticText(strategy_panel, label=label_text)
            label_font = count_label.GetFont()
            label_font.SetPointSize(8)
            count_label.SetFont(label_font)

            # 期数输入框 - 支持更大范围，9999表示所有历史数据
            # 为分析策略提供更大的期数范围支持
            max_value = 9999 if strategy_key in ['latest_absence_filter', 'digit_absence_filter', 'kill_previous_pattern_filter'] else 100
            count_spin = wx.SpinCtrl(strategy_panel, value=str(strategy_info['count']),
                                    min=1, max=max_value, size=(45, -1))
            count_spin.Bind(wx.EVT_SPINCTRL, lambda evt, key=strategy_key: self.on_count_change(evt, key))

            # 为分析策略添加工具提示
            if strategy_key in ['latest_absence_filter', 'digit_absence_filter', 'kill_previous_pattern_filter']:
                count_spin.SetToolTip("输入分析期数范围，输入大数值(如9999)可分析所有历史数据")

            # "期"标签
            unit_label = wx.StaticText(strategy_panel, label="期")
            unit_label.SetFont(wx.Font(8, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL))

            bottom_sizer.Add(count_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.RIGHT, 3)
            bottom_sizer.Add(count_spin, 0, wx.ALIGN_CENTER_VERTICAL | wx.RIGHT, 3)
            bottom_sizer.Add(unit_label, 0, wx.ALIGN_CENTER_VERTICAL)
        else:
            # 其他策略的原有控件
            # "前"标签
            count_label = wx.StaticText(strategy_panel, label="前")
            label_font = count_label.GetFont()
            label_font.SetPointSize(8)
            count_label.SetFont(label_font)

            # 数值输入框
            count_spin = wx.SpinCtrl(strategy_panel, value=str(strategy_info['count']),
                                    min=1, max=50, size=(45, -1))
            count_spin.Bind(wx.EVT_SPINCTRL, lambda evt, key=strategy_key: self.on_count_change(evt, key))

            # "个"标签
            unit_label = wx.StaticText(strategy_panel, label="个")
            unit_label.SetFont(wx.Font(8, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL))

            bottom_sizer.Add(count_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.RIGHT, 3)
            bottom_sizer.Add(count_spin, 0, wx.ALIGN_CENTER_VERTICAL | wx.RIGHT, 3)
            bottom_sizer.Add(unit_label, 0, wx.ALIGN_CENTER_VERTICAL)

        # 为大小组合、奇偶组合和位置数字筛选添加模式选择
        mode_choice = None
        if strategy_key in ['size_combo_filter', 'parity_combo_filter', 'position_digit_filter']:
            mode_choice = wx.Choice(strategy_panel, choices=["前几个", "随机", "后几个"], size=(100, -1))
            # 设置默认选择
            current_mode = strategy_info.get('mode', 'top_n')
            if current_mode == 'top_n':
                mode_choice.SetSelection(0)
            elif current_mode == 'random':
                mode_choice.SetSelection(1)
            elif current_mode == 'bottom_n':
                mode_choice.SetSelection(2)
            else:
                mode_choice.SetSelection(0)  # 默认选择"前几个"
            mode_choice.Bind(wx.EVT_CHOICE, lambda evt, key=strategy_key: self.on_mode_change(evt, key))

            # 设置字体
            mode_font = mode_choice.GetFont()
            mode_font.SetPointSize(8)
            mode_choice.SetFont(mode_font)

            bottom_sizer.Add(mode_choice, 0, wx.ALIGN_CENTER_VERTICAL | wx.LEFT, 5)

        bottom_sizer.AddStretchSpacer()  # 添加弹性空间
        strategy_sizer.Add(bottom_sizer, 0, wx.EXPAND)

        # 为分析策略添加预测数量选择控件
        if strategy_key in ['digit_absence_filter', 'latest_absence_filter']:
            # 第三行：预测数量选择
            prediction_count_sizer = wx.BoxSizer(wx.HORIZONTAL)

            prediction_count_label = wx.StaticText(strategy_panel, label="预测数量:")
            prediction_count_label.SetFont(wx.Font(8, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL))

            # 获取策略配置中的预测数量，如果没有则使用默认值1
            current_prediction_count = self.filter_strategies[strategy_key].get('prediction_count', 1)
            prediction_count_spin = wx.SpinCtrl(strategy_panel, value=str(current_prediction_count), min=1, max=10, size=(50, 22))
            prediction_count_spin.SetFont(wx.Font(8, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL))
            prediction_count_spin.Bind(wx.EVT_SPINCTRL, lambda evt, key=strategy_key: self.on_strategy_change(evt, key))

            # 确保策略配置中有正确的预测数量值
            self.filter_strategies[strategy_key]['prediction_count'] = current_prediction_count

            prediction_count_sizer.Add(prediction_count_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.RIGHT, 3)
            prediction_count_sizer.Add(prediction_count_spin, 0, wx.ALIGN_CENTER_VERTICAL)
            strategy_sizer.Add(prediction_count_sizer, 0, wx.EXPAND | wx.TOP, 3)

        # 为杀上期形态策略添加形态类型选择控件
        if strategy_key == 'kill_previous_pattern_filter':
            # 第三行：形态类型选择
            pattern_type_sizer = wx.BoxSizer(wx.HORIZONTAL)

            pattern_type_label = wx.StaticText(strategy_panel, label="形态类型:")
            pattern_type_label.SetFont(wx.Font(8, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL))

            # 形态类型选择下拉框
            pattern_choices = ['和值', '奇偶组合', '大小组合', '跨度', 'AC值']
            current_pattern_type = self.filter_strategies[strategy_key].get('pattern_type', 'sum')
            pattern_type_map = {'sum': '和值', 'parity': '奇偶组合', 'size': '大小组合', 'span': '跨度', 'ac': 'AC值'}
            current_display = pattern_type_map.get(current_pattern_type, '和值')

            pattern_type_choice = wx.Choice(strategy_panel, choices=pattern_choices, size=(70, 22))
            pattern_type_choice.SetFont(wx.Font(8, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL))
            pattern_type_choice.SetSelection(pattern_choices.index(current_display))
            pattern_type_choice.Bind(wx.EVT_CHOICE, lambda evt, key=strategy_key: self.on_pattern_type_change(evt, key))

            pattern_type_sizer.Add(pattern_type_label, 0, wx.ALIGN_CENTER_VERTICAL | wx.RIGHT, 3)
            pattern_type_sizer.Add(pattern_type_choice, 0, wx.ALIGN_CENTER_VERTICAL)

            strategy_sizer.Add(pattern_type_sizer, 0, wx.EXPAND | wx.TOP, 3)

            # 第四行：筛选功能控件
            filter_sizer = wx.BoxSizer(wx.HORIZONTAL)

            # 筛选启用复选框
            filter_checkbox = wx.CheckBox(strategy_panel, label="筛选")
            filter_checkbox.SetFont(wx.Font(8, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL))
            filter_checkbox.SetValue(self.filter_strategies[strategy_key].get('enable_filtering', False))
            filter_checkbox.Bind(wx.EVT_CHECKBOX, lambda evt, key=strategy_key: self.on_filter_enable_change(evt, key))
            filter_checkbox.SetToolTip("启用基于连续未重复次数的筛选功能")

            # 筛选阈值输入框
            filter_threshold = self.filter_strategies[strategy_key].get('filter_threshold', 10)
            threshold_spin = wx.SpinCtrl(strategy_panel, value=str(filter_threshold), min=1, max=50, size=(35, 22))
            threshold_spin.SetFont(wx.Font(8, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL))
            threshold_spin.Bind(wx.EVT_SPINCTRL, lambda evt, key=strategy_key: self.on_filter_threshold_change(evt, key))
            threshold_spin.SetToolTip("设置连续未重复次数的筛选阈值")

            filter_sizer.Add(filter_checkbox, 0, wx.ALIGN_CENTER_VERTICAL | wx.RIGHT, 2)
            filter_sizer.Add(threshold_spin, 0, wx.ALIGN_CENTER_VERTICAL)

            strategy_sizer.Add(filter_sizer, 0, wx.EXPAND | wx.TOP, 3)

        strategy_panel.SetSizer(strategy_sizer)

        # 添加边框效果
        strategy_panel.SetBackgroundColour(wx.Colour(248, 248, 248))

        # 存储控件引用
        control_dict = {
            'checkbox': checkbox,
            'count_spin': count_spin
        }
        if mode_choice:
            control_dict['mode_choice'] = mode_choice

        # 为分析策略存储预测数量控件引用
        if strategy_key in ['digit_absence_filter', 'latest_absence_filter']:
            control_dict['prediction_count_spin'] = prediction_count_spin

        # 为杀上期形态策略存储形态类型控件引用
        if strategy_key == 'kill_previous_pattern_filter':
            control_dict['pattern_type_choice'] = pattern_type_choice
            control_dict['filter_checkbox'] = filter_checkbox
            control_dict['threshold_spin'] = threshold_spin

        self.strategy_controls[strategy_key] = control_dict

        print(f"   创建简洁策略面板: {strategy_info['name']} (key: {strategy_key})")

        return strategy_panel

    def on_strategy_toggle(self, event, strategy_key):
        """策略复选框切换事件"""
        checkbox = event.GetEventObject()
        self.filter_strategies[strategy_key]['enabled'] = checkbox.GetValue()
        self.update_strategy_info()
        # 策略变更后自动筛选
        self.mark_strategy_changed()
        self.schedule_auto_filter()

    def on_count_change(self, event, strategy_key):
        """策略数量变化事件"""
        spin_ctrl = event.GetEventObject()
        self.filter_strategies[strategy_key]['count'] = spin_ctrl.GetValue()
        self.update_strategy_info()
        # 策略变更后自动筛选
        self.mark_strategy_changed()
        self.schedule_auto_filter()

    def on_mode_change(self, event, strategy_key):
        """策略模式变化事件"""
        choice_ctrl = event.GetEventObject()
        selection = choice_ctrl.GetSelection()

        # 根据选择索引确定模式
        if selection == 0:
            mode = 'top_n'
        elif selection == 1:
            mode = 'random'
        elif selection == 2:
            mode = 'bottom_n'
        else:
            mode = 'top_n'  # 默认模式

        self.filter_strategies[strategy_key]['mode'] = mode
        print(f"🔄 {strategy_key} 模式切换为: {mode}")
        # 策略变更后自动筛选
        self.mark_strategy_changed()
        self.schedule_auto_filter()

    def on_strategy_change(self, event, strategy_key):
        """策略参数变化的通用处理方法"""
        # 如果是预测数量变化，更新策略配置
        if hasattr(event.GetEventObject(), 'GetValue') and strategy_key == 'latest_absence_filter':
            if 'prediction_count_spin' in self.strategy_controls[strategy_key]:
                if event.GetEventObject() == self.strategy_controls[strategy_key]['prediction_count_spin']:
                    self.filter_strategies[strategy_key]['prediction_count'] = event.GetEventObject().GetValue()
                    print(f"🔄 {strategy_key} 预测数量变更为: {self.filter_strategies[strategy_key]['prediction_count']}")

        # 策略变更后自动筛选
        self.mark_strategy_changed()
        self.schedule_auto_filter()

    def on_pattern_type_change(self, event, strategy_key):
        """形态类型变更事件"""
        choice = event.GetEventObject()
        selection = choice.GetSelection()
        pattern_choices = ['和值', '奇偶组合', '大小组合', '跨度', 'AC值']
        pattern_type_map = {'和值': 'sum', '奇偶组合': 'parity', '大小组合': 'size', '跨度': 'span', 'AC值': 'ac'}

        if selection >= 0 and selection < len(pattern_choices):
            selected_pattern = pattern_choices[selection]
            pattern_type = pattern_type_map[selected_pattern]

            # 更新策略配置
            self.filter_strategies[strategy_key]['pattern_type'] = pattern_type

            # 重置统计数据，因为形态类型改变了
            self.filter_strategies[strategy_key]['consecutive_count'] = 0
            self.filter_strategies[strategy_key]['current_pattern'] = None
            self.filter_strategies[strategy_key]['pattern_history'] = []

            print(f"🔄 杀上期形态策略形态类型变更为: {selected_pattern} ({pattern_type})")

            self.update_strategy_info()
            # 策略变更后自动筛选
            self.mark_strategy_changed()
            self.schedule_auto_filter()



    def on_filter_enable_change(self, event, strategy_key):
        """筛选启用状态变更事件"""
        checkbox = event.GetEventObject()
        enable_filtering = checkbox.GetValue()

        # 更新策略配置
        self.filter_strategies[strategy_key]['enable_filtering'] = enable_filtering

        print(f"🔄 杀上期形态策略筛选功能{'启用' if enable_filtering else '禁用'}")

        self.update_strategy_info()
        # 策略变更后自动筛选
        self.mark_strategy_changed()
        self.schedule_auto_filter()

    def on_filter_threshold_change(self, event, strategy_key):
        """筛选阈值变更事件"""
        spin_ctrl = event.GetEventObject()
        threshold = spin_ctrl.GetValue()

        # 更新策略配置
        self.filter_strategies[strategy_key]['filter_threshold'] = threshold

        print(f"🔄 杀上期形态策略筛选阈值变更为: {threshold}期")

        self.update_strategy_info()
        # 策略变更后自动筛选
        self.mark_strategy_changed()
        self.schedule_auto_filter()

    def mark_strategy_changed(self):
        """标记策略已变更"""
        self.strategy_changed = True
        self.update_filter_status("策略已变更，准备更新...", "orange")

    def schedule_auto_filter(self):
        """延迟执行自动筛选，避免频繁触发"""
        if self.auto_filter_timer:
            self.auto_filter_timer.Stop()

        self.auto_filter_timer = wx.Timer(self)
        self.Bind(wx.EVT_TIMER, self.on_auto_filter_timer, self.auto_filter_timer)
        self.auto_filter_timer.Start(800, oneShot=True)  # 800ms延迟

    def on_auto_filter_timer(self, event):
        """自动筛选定时器事件"""
        print("🔄 策略变更，自动执行筛选...")
        self.update_filter_status("正在筛选...", "blue")

        # 通知父窗口执行筛选
        parent = self.GetParent()
        if hasattr(parent, 'execute_layer_filter'):
            parent.execute_layer_filter()

    def update_filter_status(self, message, color="green"):
        """更新筛选状态显示"""
        if hasattr(self, 'status_label'):
            self.status_label.SetLabel(f"筛选状态: {message}")

            color_map = {
                "green": wx.Colour(0, 128, 0),
                "orange": wx.Colour(255, 165, 0),
                "blue": wx.Colour(0, 100, 200),
                "red": wx.Colour(200, 0, 0)
            }
            self.status_label.SetForegroundColour(color_map.get(color, wx.Colour(0, 128, 0)))

    def mark_filter_completed(self):
        """标记筛选已完成"""
        self.strategy_changed = False
        self.last_filter_time = time.time()
        self.update_filter_status("筛选完成", "green")
        # 进度条已禁用 - 无需隐藏操作
        # if hasattr(self, 'progress_gauge'):
        #     self.progress_gauge.Hide()
        #     self.Layout()

    def update_progress(self, value, message=""):
        """更新进度条 - 已禁用进度条显示，仅保留状态消息"""
        # 进度条显示已禁用，保留筛选逻辑
        # if hasattr(self, 'progress_gauge'):
        #     self.progress_gauge.SetValue(value)
        #     if not self.progress_gauge.IsShown():
        #         self.progress_gauge.Show()
        #         self.Layout()

        # 仅更新状态消息，不显示进度条
        if message:
            self.update_filter_status(message, "blue")

    def update_strategy_info(self):
        """更新策略统计信息（简化版，无UI更新）"""
        # 获取父窗口的统计数据
        parent = self.GetParent()
        if not hasattr(parent, 'lottery_data') or not parent.lottery_data:
            print("暂无历史数据，跳过策略信息更新")
            return

        # 更新各策略的统计信息（仅用于日志输出）
        try:
            print(f"开始更新策略信息，历史数据期数: {len(parent.lottery_data)}")

            # 和值统计信息
            sum_values = set()
            for data in parent.lottery_data:
                sum_values.add(sum(data))
            sum_count = len(sum_values)
            print(f"和值统计: {sum_count}个不同和值")

            # 大小组合统计信息
            size_combos = set()
            for data in parent.lottery_data:
                large_count = sum(1 for d in data if d >= 5)
                size_combos.add(f"{large_count}大{5-large_count}小")
            print(f"大小组合: {len(size_combos)}种组合")

            # 位置数字统计信息
            position_stats = {}
            for pos in range(5):
                position_digits = set()
                for data in parent.lottery_data:
                    position_digits.add(data[pos])
                position_stats[pos+1] = len(position_digits)

            # 计算平均每位置的不同数字数量
            avg_per_position = sum(position_stats.values()) / 5 if position_stats else 0
            total_unique_digits = sum(position_stats.values())
            print(f"位置数字: 各位置统计 {position_stats}, 平均 {avg_per_position:.1f}")

            # 奇偶组合统计信息
            parity_combos = set()
            for data in parent.lottery_data:
                odd_count = sum(1 for d in data if d % 2 == 1)
                parity_combos.add(f"{odd_count}奇{5-odd_count}偶")
            print(f"奇偶组合: {len(parity_combos)}种组合")

            print("策略信息更新完成")

        except Exception as e:
            print(f"更新策略信息时出错: {e}")

    def update_filter_title(self):
        """更新筛选结果标题 - 扁平化组件无需外层标题，保留方法以维持接口兼容性"""
        # 扁平化组件设计，无外层StaticBox容器，此方法保留以维持向后兼容性
        # 筛选结果数量信息现在直接显示在筛选结果文本内容中
        pass

    def set_filter_data(self, final_numbers, process_log, result_count):
        """设置筛选数据 - 供父窗口调用"""
        self.final_filtered_numbers = final_numbers
        self.filter_process_log = process_log
        self.filter_result_count = result_count

        # 更新显示
        self.update_display()

    def update_display(self):
        """更新组件显示内容"""
        # 更新筛选过程显示
        self.filter_process_text.SetValue('\n'.join(self.filter_process_log))

        # 更新筛选结果显示
        self.display_filter_results()

        # 更新标题
        self.update_filter_title()

        # 保存当前预测记录
        self.save_current_prediction()

        # 更新主程序的预测面板显示
        if hasattr(self.GetParent(), 'update_main_prediction_display'):
            self.GetParent().update_main_prediction_display()

    def set_analysis_data(self, process_log, statistics_text="", predicted_digit="未知", predicted_digits=None):
        """设置分析数据 - 专门用于纯分析策略的结果显示"""
        self.filter_process_log = process_log
        self.filter_result_count = 0
        self.final_filtered_numbers = []

        # 更新筛选过程文本（仅显示分析日志，不包含统计信息）
        if process_log:
            process_text = '\n'.join(process_log)
            self.filter_process_text.SetValue(process_text)
            # 滚动到底部
            self.filter_process_text.SetInsertionPointEnd()

        # 在筛选结果区域显示完整的统计分析信息
        if hasattr(self, 'numbers_text'):
            if statistics_text:
                # 构建筛选结果区域的显示内容 - 简洁的三行格式
                result_content = "-----\n"
                result_content += "5星胆码\n\n"

                # 支持多数字显示
                if predicted_digits and len(predicted_digits) > 1:
                    # 多数字显示，用逗号分隔
                    digits_display = ", ".join(map(str, predicted_digits))
                    result_content += f"{digits_display}\n\n"
                    print(f"✅ 显示分析结果，预测数字: {predicted_digits} (多数字)")
                else:
                    # 单数字显示（向后兼容）
                    result_content += f"{predicted_digit}\n\n"
                    print(f"✅ 显示分析结果，预测数字: {predicted_digit} (单数字)")

                result_content += statistics_text

                self.numbers_text.SetValue(result_content)
            else:
                analysis_message = "数字连续未出现分析策略为纯分析展示功能。\n\n正在生成统计分析信息..."
                self.numbers_text.SetValue(analysis_message)
                print("⚠️ 统计信息为空，显示等待消息")

            # 滚动到顶部
            self.numbers_text.SetInsertionPoint(0)

        # 更新状态
        self.update_filter_status("分析完成", "blue")

    def display_filter_results(self):
        """显示筛选结果，与kill_number_display_wx.py保持一致的数据格式"""
        if not self.final_filtered_numbers:
            self.numbers_text.SetValue("筛选结果为空")
            return

        result_lines = []
        # 与kill_number_display_wx.py保持一致的标题格式
        result_lines.append("--------")
        result_lines.append("智能5星筛选数字")
        result_lines.append("")
        result_lines.append(f"筛选结果（共{len(self.final_filtered_numbers)}注）:")

        # 显示所有筛选结果，按行显示，每行10个号码（与kill_number_display_wx.py一致）
        for i in range(0, len(self.final_filtered_numbers), self.numbers_per_row):
            line_numbers = []
            for j in range(i, min(i + self.numbers_per_row, len(self.final_filtered_numbers))):
                line_numbers.append(f"{self.final_filtered_numbers[j]:05d}")
            result_lines.append(" ".join(line_numbers))

        # 检查是否启用了分析策略，如果是则附加统计信息
        parent = self.GetParent()
        if hasattr(parent, 'filter_component') and hasattr(parent.filter_component, 'filter_strategies'):
            strategies = parent.filter_component.filter_strategies

            # 数字连续未出现分析策略
            if 'digit_absence_filter' in strategies and strategies['digit_absence_filter']['enabled']:
                strategy = strategies['digit_absence_filter']
                if 'statistics_text' in strategy:
                    result_lines.append("")
                    result_lines.append("=" * 50)
                    result_lines.append(f"🎯 数字连续未出现分析结果")
                    # 支持多数字显示
                    predicted_digits = strategy.get('predicted_digits', [])
                    if predicted_digits and len(predicted_digits) > 1:
                        result_lines.append(f"预测数字: {predicted_digits}")
                    else:
                        result_lines.append(f"预测数字: {strategy.get('predicted_digit', '未知')}")
                    result_lines.append("=" * 50)
                    result_lines.append(strategy['statistics_text'])

            # 最新未出现最少分析策略
            if 'latest_absence_filter' in strategies and strategies['latest_absence_filter']['enabled']:
                strategy = strategies['latest_absence_filter']
                if 'statistics_text' in strategy and 'predicted_digit' in strategy:
                    result_lines.append("")
                    result_lines.append("=" * 50)
                    result_lines.append(f"🎯 最新未出现最少分析结果")
                    result_lines.append(f"预测数字: {strategy['predicted_digit']}")
                    result_lines.append("=" * 50)
                    result_lines.append(strategy['statistics_text'])

            # 杀上期形态分析策略
            if 'kill_previous_pattern_filter' in strategies and strategies['kill_previous_pattern_filter']['enabled']:
                strategy = strategies['kill_previous_pattern_filter']
                if 'statistics_text' in strategy:
                    result_lines.append("")
                    result_lines.append("=" * 50)
                    result_lines.append(f"🎯 杀上期形态分析结果")
                    pattern_type_name = self.get_pattern_type_name(strategy.get('pattern_type', 'sum'))
                    current_pattern = strategy.get('current_pattern', '未知')
                    consecutive_count = strategy.get('consecutive_count', 0)
                    repeat_probability = strategy.get('repeat_probability', 0)
                    result_lines.append(f"形态类型: {pattern_type_name}")
                    result_lines.append(f"当前形态值: {current_pattern}")
                    result_lines.append(f"连续未重复: {consecutive_count}期")
                    result_lines.append(f"重复概率: {repeat_probability:.1f}%")
                    result_lines.append("=" * 50)
                    result_lines.append(strategy['statistics_text'])

        # 使用与kill_number_display_wx.py相同的控件名称
        self.numbers_text.SetValue('\n'.join(result_lines))

    def get_numbers_text_content(self):
        """获取筛选结果文本内容 - 提供外部访问接口，与kill_number_display_wx.py保持一致"""
        if hasattr(self, 'numbers_text') and self.numbers_text:
            return self.numbers_text.GetValue()
        return ""

    def get_filtered_numbers(self):
        """获取筛选后的号码列表 - 提供外部访问接口"""
        return self.final_filtered_numbers.copy() if self.final_filtered_numbers else []

    def get_filter_count(self):
        """获取筛选结果数量 - 提供外部访问接口"""
        return self.filter_result_count

    # 重新筛选方法已移除
    def on_clear_filter(self, event):
        """清空结果按钮事件"""
        self.filter_process_text.Clear()
        if hasattr(self, 'numbers_text'):
            self.numbers_text.Clear()
        self.filter_result_count = 0
        self.final_filtered_numbers = []
        self.filter_process_log = []
        self.update_filter_status("已清空结果", "green")

    def get_random_size_patterns(self, count):
        """生成随机大小组合模式"""
        import random
        from itertools import product

        # 生成所有可能的大小组合（2^5 = 32种）
        all_patterns = []
        for combination in product(['小', '大'], repeat=5):
            pattern = ''.join(combination)
            all_patterns.append(pattern)

        # 随机选择指定数量的组合
        selected_patterns = random.sample(all_patterns, min(count, len(all_patterns)))
        print(f"🎲 随机选择大小组合: {selected_patterns}")
        return selected_patterns

    def get_random_parity_patterns(self, count):
        """生成随机奇偶组合模式"""
        import random
        from itertools import product

        # 生成所有可能的奇偶组合（2^5 = 32种）
        all_patterns = []
        for combination in product(['偶', '奇'], repeat=5):
            pattern = ''.join(combination)
            all_patterns.append(pattern)

        # 随机选择指定数量的组合
        selected_patterns = random.sample(all_patterns, min(count, len(all_patterns)))
        print(f"🎲 随机选择奇偶组合: {selected_patterns}")
        return selected_patterns

    def get_random_position_digits(self, count_per_position):
        """生成随机位置数字组合"""
        import random

        excluded_digits = []

        # 为每个位置随机选择指定数量的数字
        for pos in range(1, 6):
            # 每个位置可选择的数字是0-9
            available_digits = list(range(10))
            # 随机选择count_per_position个数字
            selected_digits = random.sample(available_digits, min(count_per_position, len(available_digits)))

            for digit in selected_digits:
                excluded_digits.append(f"第{pos}位-{digit}")

        print(f"🎲 随机选择位置数字: {excluded_digits}")
        return excluded_digits

    def load_prediction_records(self):
        """加载预测记录"""
        import json
        try:
            if os.path.exists(self.prediction_file):
                with open(self.prediction_file, 'r', encoding='utf-8') as f:
                    self.prediction_records = json.load(f)
                print(f"📊 加载了 {len(self.prediction_records)} 条预测记录")
            else:
                self.prediction_records = []
                print("📊 未找到历史预测记录，创建新记录")
        except Exception as e:
            print(f"❌ 加载预测记录失败: {e}")
            self.prediction_records = []

    def save_prediction_records(self):
        """保存预测记录"""
        import json
        try:
            with open(self.prediction_file, 'w', encoding='utf-8') as f:
                json.dump(self.prediction_records, f, ensure_ascii=False, indent=2)
            print(f"💾 已保存 {len(self.prediction_records)} 条预测记录")
        except Exception as e:
            print(f"❌ 保存预测记录失败: {e}")

    def save_current_prediction(self):
        """保存当前筛选结果作为预测记录"""
        if not self.final_filtered_numbers:
            return

        import time
        current_time = time.strftime('%Y-%m-%d %H:%M:%S')

        # 创建预测记录
        prediction = {
            'timestamp': current_time,
            'predicted_numbers': self.final_filtered_numbers.copy(),
            'count': len(self.final_filtered_numbers),
            'result': None,  # 待验证
            'lottery_number': None,  # 待填入开奖号码
            'period': None  # 待填入期号
        }

        self.prediction_records.append(prediction)
        self.save_prediction_records()
        print(f"📝 已保存预测记录: {len(self.final_filtered_numbers)} 注")

    # on_clear_predictions 方法已移除，使用主程序的预测面板

    def on_clear_predictions_from_filter(self, event):
        """从筛选组件清空预测记录按钮事件"""
        dlg = wx.MessageDialog(self, "确定要清空所有预测记录吗？", "确认清空", wx.YES_NO | wx.ICON_QUESTION)
        if dlg.ShowModal() == wx.ID_YES:
            self.prediction_records = []
            self.save_prediction_records()
            # 更新主程序的预测面板显示
            if hasattr(self.GetParent(), 'update_main_prediction_display'):
                self.GetParent().update_main_prediction_display()
            print("🗑️ 已清空所有预测记录")
        dlg.Destroy()

    def on_refresh_predictions_from_filter(self, event):
        """从筛选组件刷新预测统计按钮事件"""
        # 更新主程序的预测面板显示
        if hasattr(self.GetParent(), 'update_main_prediction_display'):
            self.GetParent().update_main_prediction_display()
        print("🔄 已刷新预测统计显示")

    # on_refresh_predictions 方法已移除，使用主程序的预测面板

    # update_prediction_display 方法已移除，使用主程序的预测面板显示

    def check_prediction_hits(self, new_lottery_number, period_info=None):
        """检查预测命中情况 - 修复版本，添加详细调试"""
        if not self.prediction_records:
            print("📊 暂无预测记录，跳过命中检查")
            return

        print(f"🔍 开始检查命中情况，开奖号码: {new_lottery_number} (类型: {type(new_lottery_number)})")

        # 查找最近的未验证预测记录
        updated = False
        for i, record in enumerate(reversed(self.prediction_records)):
            if record.get('result') is None:  # 未验证的记录
                record_index = len(self.prediction_records) - 1 - i
                print(f"📋 检查记录 {record_index}: {record.get('period', '未知期号')}")

                # 检查新开奖号码是否在预测结果中
                predicted_numbers = record.get('predicted_numbers', [])
                print(f"📊 预测数字总数: {len(predicted_numbers)}")

                if predicted_numbers:
                    # 显示前几个预测数字的格式
                    sample_numbers = predicted_numbers[:5]
                    print(f"🔍 预测数字样本: {sample_numbers}")
                    print(f"🔍 样本数字类型: {[type(num) for num in sample_numbers]}")

                # 数据格式统一处理
                # 将开奖号码转换为不同格式进行匹配
                lottery_str = str(new_lottery_number)
                lottery_int = int(new_lottery_number) if isinstance(new_lottery_number, str) else new_lottery_number
                lottery_padded = f"{lottery_int:05d}"  # 5位前导零格式

                print(f"🔍 开奖号码格式化:")
                print(f"   字符串格式: '{lottery_str}'")
                print(f"   整数格式: {lottery_int}")
                print(f"   5位格式: '{lottery_padded}'")

                # 多种格式匹配
                is_hit = False
                match_format = None

                # 方法1: 直接匹配
                if new_lottery_number in predicted_numbers:
                    is_hit = True
                    match_format = "直接匹配"
                # 方法2: 字符串格式匹配
                elif lottery_str in predicted_numbers:
                    is_hit = True
                    match_format = "字符串匹配"
                # 方法3: 整数格式匹配
                elif lottery_int in predicted_numbers:
                    is_hit = True
                    match_format = "整数匹配"
                # 方法4: 5位格式匹配
                elif lottery_padded in predicted_numbers:
                    is_hit = True
                    match_format = "5位格式匹配"
                # 方法5: 转换预测数字格式后匹配
                else:
                    # 将预测数字转换为不同格式进行匹配
                    for pred_num in predicted_numbers:
                        try:
                            if isinstance(pred_num, str):
                                pred_int = int(pred_num)
                                pred_padded = f"{pred_int:05d}"
                            else:
                                pred_int = pred_num
                                pred_padded = f"{pred_num:05d}"

                            if (lottery_int == pred_int or
                                lottery_str == str(pred_num) or
                                lottery_padded == pred_padded):
                                is_hit = True
                                match_format = f"转换匹配 (预测: {pred_num})"
                                break
                        except (ValueError, TypeError):
                            continue

                print(f"🎯 匹配结果: {'命中' if is_hit else '未命中'}")
                if is_hit:
                    print(f"✅ 匹配方式: {match_format}")

                # 更新记录
                record['result'] = '中' if is_hit else '挂'
                record['lottery_number'] = new_lottery_number
                record['period'] = period_info or f"期号{len(self.prediction_records)}"

                # 保存更新后的记录
                self.save_prediction_records()

                # 更新主程序的预测面板显示
                if hasattr(self.GetParent(), 'update_main_prediction_display'):
                    self.GetParent().update_main_prediction_display()

                # 打印结果
                result_text = "命中" if is_hit else "未命中"
                print(f"🎯 预测结果: {new_lottery_number} {result_text} (预测范围: {len(predicted_numbers)}注)")

                updated = True
                break  # 只处理最近的一条未验证记录

        if not updated:
            print(f"📊 所有预测记录已验证，新开奖号码: {new_lottery_number}")

        # 检查分析策略的命中情况
        self.check_digit_absence_prediction(new_lottery_number)
        self.check_latest_absence_prediction(new_lottery_number)

    def check_digit_absence_prediction(self, new_lottery_number):
        """检查数字连续未出现分析的预测命中情况"""
        strategy = self.filter_strategies.get('digit_absence_filter')
        if not strategy or not strategy['enabled']:
            return

        # 获取预测数字列表，优先使用新的多数字预测
        predicted_digits = strategy.get('predicted_digits', [])
        if not predicted_digits:
            # 向后兼容：如果没有预测数字列表，使用单个预测数字
            predicted_digit = strategy.get('predicted_digit')
            if predicted_digit is None:
                return
            predicted_digits = [predicted_digit]

        # 将开奖号码转换为字符串格式
        lottery_str = str(new_lottery_number)
        if len(lottery_str) < 5:
            lottery_str = f"{int(lottery_str):05d}"  # 补齐前导零

        # 检查任意一个预测数字是否在开奖号码中
        is_hit = False
        hit_digits = []
        for digit in predicted_digits:
            digit_str = str(digit)
            if digit_str in lottery_str:
                is_hit = True
                hit_digits.append(digit)

        # 更新统计数据
        strategy['total_predictions'] = strategy.get('total_predictions', 0) + 1
        if is_hit:
            strategy['correct_predictions'] = strategy.get('correct_predictions', 0) + 1

        # 计算命中率
        total = strategy['total_predictions']
        correct = strategy['correct_predictions']
        hit_rate = (correct / total * 100) if total > 0 else 0.0
        strategy['hit_rate'] = hit_rate

        # 数字连续最多策略不再显示UI控件，仅保留数据更新

        print(f"🎯 数字连续未出现分析预测检查:")
        print(f"   预测数字: {predicted_digits}")
        print(f"   开奖号码: {lottery_str}")
        if is_hit:
            print(f"   预测结果: 命中 (命中数字: {hit_digits})")
        else:
            print(f"   预测结果: 未命中")
        print(f"   累计统计: {correct}/{total} = {hit_rate:.1f}%")

        # 更新主程序的预测面板显示
        if hasattr(self, 'GetParent') and hasattr(self.GetParent(), 'update_main_prediction_display'):
            self.GetParent().update_main_prediction_display()

    def check_latest_absence_prediction(self, new_lottery_number):
        """检查最新未出现最少分析的预测命中情况"""
        strategy = self.filter_strategies.get('latest_absence_filter')
        if not strategy or not strategy['enabled']:
            return

        # 获取预测数字列表，优先使用新的多数字预测
        predicted_digits = strategy.get('predicted_digits', [])
        if not predicted_digits:
            # 向后兼容：如果没有预测数字列表，使用单个预测数字
            predicted_digit = strategy.get('predicted_digit')
            if predicted_digit is None:
                return
            predicted_digits = [predicted_digit]

        # 将开奖号码转换为字符串格式
        lottery_str = str(new_lottery_number)
        if len(lottery_str) < 5:
            lottery_str = f"{int(lottery_str):05d}"  # 补齐前导零

        # 检查任意一个预测数字是否在开奖号码中
        is_hit = False
        hit_digits = []
        for digit in predicted_digits:
            digit_str = str(digit)
            if digit_str in lottery_str:
                is_hit = True
                hit_digits.append(digit)

        # 更新统计数据
        strategy['total_predictions'] = strategy.get('total_predictions', 0) + 1
        if is_hit:
            strategy['correct_predictions'] = strategy.get('correct_predictions', 0) + 1

        # 计算命中率
        total = strategy['total_predictions']
        correct = strategy['correct_predictions']
        hit_rate = (correct / total * 100) if total > 0 else 0.0
        strategy['hit_rate'] = hit_rate

        # 未出现最少分析策略不再显示UI控件，仅保留数据更新

        print(f"🎯 最新未出现最少分析预测检查:")
        print(f"   预测数字: {predicted_digits}")
        print(f"   开奖号码: {lottery_str}")
        if is_hit:
            print(f"   预测结果: 命中 (命中数字: {hit_digits})")
        else:
            print(f"   预测结果: 未命中")
        print(f"   累计统计: {correct}/{total} = {hit_rate:.1f}%")

        # 更新主程序的预测面板显示
        if hasattr(self, 'GetParent') and hasattr(self.GetParent(), 'update_main_prediction_display'):
            self.GetParent().update_main_prediction_display()

class LotteryDataAnalysisApp(wx.Frame):
    def __init__(self):
        super().__init__(None, title="彩票数据分析工具", size=(885, 900))

        # 设置窗口居中显示
        self.Center()

        # 数据存储
        self.lottery_data = []  # 存储历史彩票数据
        self.data_file_path = r"D:\辉达挂机软件\OpenCode\HASHFFC.txt"



        # 文件监控相关
        self.last_file_mtime = 0  # 文件最后修改时间
        self.auto_update_timer = None  # 自动更新定时器

        self.init_ui()
        self.load_data()
        self.start_auto_update()

        # 绑定窗口关闭事件
        self.Bind(wx.EVT_CLOSE, self.on_close)
        
    def init_ui(self):
        """初始化用户界面"""
        # 创建主面板
        main_panel = wx.Panel(self)
        main_sizer = wx.BoxSizer(wx.VERTICAL)

        # 标题
        title_label = wx.StaticText(main_panel, label="彩票历史数据统计分析")
        title_font = wx.Font(16, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD)
        title_label.SetFont(title_font)
        main_sizer.Add(title_label, 0, wx.ALL | wx.CENTER, 5)

        # 文件路径显示和刷新按钮 - 紧凑布局
        file_sizer = wx.BoxSizer(wx.HORIZONTAL)
        file_label = wx.StaticText(main_panel, label="数据文件:")
        file_sizer.Add(file_label, 0, wx.ALL | wx.ALIGN_CENTER_VERTICAL, 2)

        self.file_path_text = wx.TextCtrl(main_panel, value=self.data_file_path, style=wx.TE_READONLY, size=(400, 28))
        file_sizer.Add(self.file_path_text, 0, wx.ALL, 2)

        refresh_btn = wx.Button(main_panel, label="更新数据源", size=(90, 28))
        refresh_btn.SetToolTip("从数据文件重新加载最新的开奖记录，并自动执行筛选")
        refresh_btn.Bind(wx.EVT_BUTTON, self.on_refresh_data)
        file_sizer.Add(refresh_btn, 0, wx.ALL, 2)

        # 浏览文件按钮
        browse_btn = wx.Button(main_panel, label="浏览", size=(60, 28))
        browse_btn.Bind(wx.EVT_BUTTON, self.on_browse_file)
        file_sizer.Add(browse_btn, 0, wx.ALL, 2)

        # 导出结果按钮
        export_btn = wx.Button(main_panel, label="导出结果", size=(80, 28))
        export_btn.Bind(wx.EVT_BUTTON, self.on_export_results)
        file_sizer.Add(export_btn, 0, wx.ALL, 2)

        main_sizer.Add(file_sizer, 0, wx.ALL | wx.CENTER, 2)

        # 数据统计信息
        self.data_info_label = wx.StaticText(main_panel, label="数据加载中...")
        main_sizer.Add(self.data_info_label, 0, wx.ALL | wx.CENTER, 1)
        
        # 创建统计区域的容器 - 左侧2行2列统计，右侧预测面板
        content_sizer = wx.BoxSizer(wx.HORIZONTAL)

        # 左侧：4个统计区域（2行2列）
        left_stats_sizer = wx.BoxSizer(wx.VERTICAL)
        top_stats_sizer = wx.GridSizer(2, 2, 5, 5)

        # 1. 和值统计区域
        sum_box = wx.StaticBoxSizer(wx.VERTICAL, main_panel, "和值统计")
        self.sum_list = wx.ListCtrl(main_panel, style=wx.LC_REPORT | wx.LC_SINGLE_SEL, size=(320, 250))
        self.sum_list.AppendColumn("序号", width=40)
        self.sum_list.AppendColumn("和值", width=50)
        self.sum_list.AppendColumn("未出现期数", width=80)
        self.sum_list.AppendColumn("概率", width=60)
        sum_box.Add(self.sum_list, 1, wx.EXPAND | wx.ALL, 2)
        top_stats_sizer.Add(sum_box, 1, wx.EXPAND)

        # 2. 位置数字统计区域
        position_box = wx.StaticBoxSizer(wx.VERTICAL, main_panel, "位置数字统计")
        self.position_list = wx.ListCtrl(main_panel, style=wx.LC_REPORT | wx.LC_SINGLE_SEL, size=(320, 250))
        self.position_list.AppendColumn("序号", width=40)
        self.position_list.AppendColumn("位置-数字", width=70)
        self.position_list.AppendColumn("未出现期数", width=80)
        self.position_list.AppendColumn("概率", width=60)
        position_box.Add(self.position_list, 1, wx.EXPAND | wx.ALL, 2)
        top_stats_sizer.Add(position_box, 1, wx.EXPAND)

        # 3. 大小组合统计区域
        size_box = wx.StaticBoxSizer(wx.VERTICAL, main_panel, "大小组合统计")
        self.size_list = wx.ListCtrl(main_panel, style=wx.LC_REPORT | wx.LC_SINGLE_SEL, size=(320, 250))
        self.size_list.AppendColumn("序号", width=40)
        self.size_list.AppendColumn("大小组合", width=90)
        self.size_list.AppendColumn("未出现期数", width=80)
        self.size_list.AppendColumn("概率", width=60)
        size_box.Add(self.size_list, 1, wx.EXPAND | wx.ALL, 2)
        top_stats_sizer.Add(size_box, 1, wx.EXPAND)

        # 4. 奇偶组合统计区域
        parity_box = wx.StaticBoxSizer(wx.VERTICAL, main_panel, "奇偶组合统计")
        self.parity_list = wx.ListCtrl(main_panel, style=wx.LC_REPORT | wx.LC_SINGLE_SEL, size=(320, 250))
        self.parity_list.AppendColumn("序号", width=40)
        self.parity_list.AppendColumn("奇偶组合", width=90)
        self.parity_list.AppendColumn("未出现期数", width=80)
        self.parity_list.AppendColumn("概率", width=60)
        parity_box.Add(self.parity_list, 1, wx.EXPAND | wx.ALL, 2)
        top_stats_sizer.Add(parity_box, 1, wx.EXPAND)

        left_stats_sizer.Add(top_stats_sizer, 1, wx.EXPAND)
        content_sizer.Add(left_stats_sizer, 3, wx.ALL | wx.EXPAND, 3)

        # 右侧：预测命中率统计面板（扩展填充更多空间）
        right_panel_sizer = wx.BoxSizer(wx.VERTICAL)

        # 创建独立的可调整预测面板 - 可以通过参数轻松调整高度
        PREDICTION_PANEL_HEIGHT = 700  # 🎯 在这里调整预测面板高度
        MIN_HEIGHT = 400               # 最小高度
        MAX_HEIGHT = 900               # 最大高度

        self.main_prediction_panel = self.create_main_prediction_panel(
            main_panel,
            panel_height=PREDICTION_PANEL_HEIGHT,
            min_height=MIN_HEIGHT,
            max_height=MAX_HEIGHT
        )
        # 使用固定尺寸，不受其他组件影响
        right_panel_sizer.Add(self.main_prediction_panel, 0, wx.ALL, 5)

        # 预测控制按钮已移动到预测面板内部

        content_sizer.Add(right_panel_sizer, 3, wx.ALL | wx.EXPAND, 3)

        main_sizer.Add(content_sizer, 3, wx.ALL | wx.EXPAND, 3)

        # 下半部分：创建独立的筛选结果组件（移除内部预测面板）
        self.filter_component = FilterResultComponent(main_panel)
        main_sizer.Add(self.filter_component, 0, wx.ALL | wx.EXPAND, 3)
        
        main_panel.SetSizer(main_sizer)

        # 强制刷新整个布局，确保预测面板尺寸生效
        main_panel.Layout()
        self.Layout()
        self.Refresh()

    def create_main_prediction_panel(self, parent, panel_height=600, min_height=400, max_height=800):
        """在主程序中创建独立的可调整预测记录面板

        Args:
            parent: 父容器
            panel_height: 面板总高度 (默认600px)
            min_height: 最小高度 (默认400px)
            max_height: 最大高度 (默认800px)
        """
        # 创建独立的面板容器
        panel = wx.Panel(parent)

        # 设置可调整的高度参数
        actual_height = max(min_height, min(panel_height, max_height))
        panel.SetMinSize((220, actual_height))
        panel.SetMaxSize((400, actual_height))
        panel.SetSize((220, actual_height))

        panel_sizer = wx.BoxSizer(wx.VERTICAL)

        print(f"🎯 创建预测面板: 高度={actual_height}px (范围: {min_height}-{max_height}px)")

        # 标题
        title_label = wx.StaticText(panel, label="预测命中率统计")
        title_font = wx.Font(12, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD)
        title_label.SetFont(title_font)
        panel_sizer.Add(title_label, 0, wx.ALL | wx.CENTER, 5)

        # 最新开奖号码显示
        self.latest_number_label = wx.StaticText(panel, label="最新开奖: 等待数据...")
        latest_font = wx.Font(10, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD)
        self.latest_number_label.SetFont(latest_font)
        panel_sizer.Add(self.latest_number_label, 0, wx.ALL | wx.CENTER, 3)

        # 统计信息显示
        stats_sizer = wx.BoxSizer(wx.HORIZONTAL)

        # 左列统计
        left_stats_sizer = wx.BoxSizer(wx.VERTICAL)
        self.hit_rate_label = wx.StaticText(panel, label="命中率: 0%")
        self.consecutive_hits_label = wx.StaticText(panel, label="最大连中: 0次")
        left_stats_sizer.Add(self.hit_rate_label, 0, wx.ALL, 2)
        left_stats_sizer.Add(self.consecutive_hits_label, 0, wx.ALL, 2)

        # 右列统计
        right_stats_sizer = wx.BoxSizer(wx.VERTICAL)
        self.total_predictions_label = wx.StaticText(panel, label="总预测: 0次")
        self.consecutive_misses_label = wx.StaticText(panel, label="最大连挂: 0次")
        right_stats_sizer.Add(self.total_predictions_label, 0, wx.ALL, 2)
        right_stats_sizer.Add(self.consecutive_misses_label, 0, wx.ALL, 2)

        stats_sizer.Add(left_stats_sizer, 1, wx.EXPAND)
        stats_sizer.Add(right_stats_sizer, 1, wx.EXPAND)
        panel_sizer.Add(stats_sizer, 0, wx.ALL | wx.EXPAND, 5)

        # 分析策略命中率统计
        analysis_label = wx.StaticText(panel, label="分析策略命中率:")
        analysis_font = wx.Font(10, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD)
        analysis_label.SetFont(analysis_font)
        panel_sizer.Add(analysis_label, 0, wx.ALL, 3)

        # 分析策略统计信息
        analysis_stats_sizer = wx.BoxSizer(wx.VERTICAL)

        # 数字连续未出现分析策略
        self.digit_absence_stats_label = wx.StaticText(panel, label="数字连续未出现分析: 0/0 (0.0%)")
        self.digit_absence_stats_label.SetFont(wx.Font(9, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL))
        analysis_stats_sizer.Add(self.digit_absence_stats_label, 0, wx.ALL, 2)

        # 未出现最少分析策略
        self.latest_absence_stats_label = wx.StaticText(panel, label="未出现最少分析: 0/0 (0.0%)")
        self.latest_absence_stats_label.SetFont(wx.Font(9, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL))
        analysis_stats_sizer.Add(self.latest_absence_stats_label, 0, wx.ALL, 2)

        panel_sizer.Add(analysis_stats_sizer, 0, wx.ALL | wx.EXPAND, 5)

        # 历史记录列表 - 根据容器高度自动调整
        record_label = wx.StaticText(panel, label="历史记录:")
        panel_sizer.Add(record_label, 0, wx.ALL, 3)

        # 计算列表高度：总高度 - 固定组件高度 - 边距 - 按钮高度
        fixed_components_height = 160  # 标题+统计信息+标签+按钮+边距的大概高度
        list_height = max(200, actual_height - fixed_components_height)

        self.main_record_list = wx.ListCtrl(panel, style=wx.LC_REPORT | wx.LC_SINGLE_SEL)
        self.main_record_list.AppendColumn("开奖号码", width=100)
        self.main_record_list.AppendColumn("结果", width=60)

        # 设置自适应的列表高度
        self.main_record_list.SetMinSize((-1, list_height))
        self.main_record_list.SetSize((-1, list_height))
        panel_sizer.Add(self.main_record_list, 1, wx.ALL | wx.EXPAND, 3)

        print(f"📋 历史记录列表高度: {list_height}px (容器高度: {actual_height}px)")

        # 添加预测控制按钮到面板底部
        button_sizer = wx.BoxSizer(wx.HORIZONTAL)
        button_sizer.AddStretchSpacer()  # 左侧弹性空间，让按钮右对齐

        self.clear_predictions_btn = wx.Button(panel, label="清空记录", size=(80, 25))
        self.clear_predictions_btn.Bind(wx.EVT_BUTTON, self.on_clear_main_predictions)
        button_sizer.Add(self.clear_predictions_btn, 0, wx.ALL, 2)

        self.refresh_predictions_btn = wx.Button(panel, label="刷新统计", size=(80, 25))
        self.refresh_predictions_btn.Bind(wx.EVT_BUTTON, self.on_refresh_main_predictions)
        button_sizer.Add(self.refresh_predictions_btn, 0, wx.ALL, 2)

        panel_sizer.Add(button_sizer, 0, wx.ALL | wx.EXPAND, 3)
        print("🔘 预测控制按钮已添加到面板底部")

        panel.SetSizer(panel_sizer)

        # 强制刷新布局，确保尺寸设置生效
        panel.Layout()
        panel.Refresh()

        return panel

    def adjust_prediction_panel_height(self, new_height):
        """动态调整预测面板高度的便捷方法

        Args:
            new_height: 新的面板高度
        """
        if hasattr(self, 'main_prediction_panel') and hasattr(self, 'main_record_list'):
            # 重新计算列表高度
            fixed_components_height = 120
            list_height = max(200, new_height - fixed_components_height)

            # 调整面板尺寸
            self.main_prediction_panel.SetMinSize((220, new_height))
            self.main_prediction_panel.SetSize((220, new_height))

            # 调整列表尺寸
            self.main_record_list.SetMinSize((-1, list_height))
            self.main_record_list.SetSize((-1, list_height))

            # 刷新布局
            self.main_prediction_panel.Layout()
            self.Layout()
            self.Refresh()

            print(f"🎯 预测面板高度已调整为: {new_height}px, 列表高度: {list_height}px")

    def on_clear_main_predictions(self, event):
        """清空主预测记录按钮事件 - 直接清空，无确认弹窗"""
        print("🔘 清空记录按钮被点击！")

        # 安全检查：验证预测组件是否正确初始化
        if not hasattr(self, 'filter_component'):
            print("❌ 错误：filter_component 不存在")
            wx.MessageBox("预测组件未初始化", "错误", wx.OK | wx.ICON_ERROR)
            return

        print("✅ filter_component 存在，继续执行清空操作")

        # 检查是否有记录需要清空
        record_count = len(self.filter_component.prediction_records)
        if record_count == 0:
            wx.MessageBox("当前没有预测记录需要清空", "提示", wx.OK | wx.ICON_INFORMATION)
            return

        # 直接执行清空操作，无需确认
        try:
            print(f"🗑️ 开始清空 {record_count} 条预测记录...")

            # 清空预测记录数据
            self.filter_component.prediction_records = []
            self.filter_component.save_prediction_records()

            # 立即更新显示
            self.update_main_prediction_display()

            # 显示成功反馈
           

            print(f"✅ 成功清空了 {record_count} 条预测记录，统计已重置")

        except Exception as e:
            wx.MessageBox(f"清空预测记录时发生错误：{str(e)}", "错误", wx.OK | wx.ICON_ERROR)
            print(f"❌ 清空预测记录失败: {str(e)}")

    def on_refresh_main_predictions(self, event):
        """刷新主预测统计按钮事件 - 优化用户体验"""
        print("🔄 刷新统计按钮被点击！")

        if not hasattr(self, 'filter_component'):
            print("❌ 错误：filter_component 不存在")
            wx.MessageBox("预测组件未初始化", "错误", wx.OK | wx.ICON_ERROR)
            return

        print("✅ filter_component 存在，继续执行刷新操作")

        try:
            record_count = len(self.filter_component.prediction_records)
            print(f"🔄 开始刷新预测统计，当前记录数: {record_count}")

            # 更新显示
            self.update_main_prediction_display()

            # 显示刷新成功的反馈
            if record_count > 0:
                # 获取最新的统计信息
                hits = sum(1 for record in self.filter_component.prediction_records if record.get('result') == '中')
                hit_rate = (hits / record_count * 100) if record_count > 0 else 0

                wx.MessageBox(
                    f"预测统计已刷新！\n\n"
                    f"当前统计信息：\n"
                    f"• 总预测次数: {record_count}\n"
                    f"• 命中次数: {hits}\n"
                    f"• 命中率: {hit_rate:.1f}%\n"
                    f"• 历史记录已更新",
                    "刷新完成",
                    wx.OK | wx.ICON_INFORMATION
                )
            else:
                wx.MessageBox("预测统计已刷新！\n当前没有预测记录。", "刷新完成", wx.OK | wx.ICON_INFORMATION)

            print(f"✅ 预测统计刷新完成，记录数: {record_count}")

        except Exception as e:
            wx.MessageBox(f"刷新预测统计时发生错误：{str(e)}", "错误", wx.OK | wx.ICON_ERROR)
            print(f"❌ 刷新预测统计失败: {str(e)}")

    def update_main_prediction_display(self):
        """更新主预测统计显示"""
        print(f"🔄 开始更新预测面板显示...")

        if not hasattr(self, 'filter_component'):
            print("❌ 错误：filter_component 不存在")
            return

        if not hasattr(self, 'main_record_list'):
            print("❌ 错误：main_record_list 不存在")
            return

        # 清空列表
        self.main_record_list.DeleteAllItems()

        # 获取预测记录
        prediction_records = self.filter_component.prediction_records
        print(f"📊 获取到 {len(prediction_records)} 条预测记录")

        # 数据验证：检查预测记录的数据结构
        if prediction_records:
            print("🔍 预测记录数据验证:")
            valid_results = 0
            for i, record in enumerate(prediction_records[-5:]):  # 检查最新5条记录
                lottery_num = record.get('lottery_number', '无')
                result = record.get('result', '无')
                period = record.get('period', '无')
                print(f"  记录 {len(prediction_records)-4+i}: 期号={period}, 开奖={lottery_num}, 结果={result}")
                if result in ['中', '挂']:
                    valid_results += 1
            print(f"📈 最近5条记录中有 {valid_results} 条已验证结果")

        # 计算统计数据 - 修复版本，只基于已验证记录
        # 获取已验证记录进行统计计算
        verified_records_for_stats = [r for r in prediction_records if r.get('result') in ['中', '挂']]
        total_predictions = len(verified_records_for_stats)  # 只计算已验证的记录
        hits = sum(1 for record in verified_records_for_stats if record.get('result') == '中')
        hit_rate = (hits / total_predictions * 100) if total_predictions > 0 else 0

        print(f"📊 统计计算修正: 总记录={len(prediction_records)}, 已验证记录={total_predictions}, 命中={hits}, 命中率={hit_rate:.1f}%")

        # 计算历史最大连续命中和连续未命中次数
        max_consecutive_hits = 0
        max_consecutive_misses = 0

        print(f"🔍 开始计算历史最大连中连挂统计，总记录数: {total_predictions}")

        # 获取所有已验证记录，按时间顺序排列
        verified_records = [r for r in prediction_records if r.get('result') in ['中', '挂']]
        print(f"📊 已验证记录数: {len(verified_records)}")

        if verified_records:
            # 遍历所有记录，计算历史最大连续次数
            current_hits = 0
            current_misses = 0

            print("📈 开始扫描历史记录计算最大连续次数:")
            for i, record in enumerate(verified_records):
                result = record.get('result')
                lottery_num = record.get('lottery_number', '未知')
                period = record.get('period', f'第{i+1}期')

                if result == '中':
                    current_hits += 1
                    current_misses = 0  # 重置连挂计数
                    max_consecutive_hits = max(max_consecutive_hits, current_hits)
                    print(f"  {period}: {lottery_num} -> 中 | 当前连中: {current_hits}, 历史最大连中: {max_consecutive_hits}")
                elif result == '挂':
                    current_misses += 1
                    current_hits = 0  # 重置连中计数
                    max_consecutive_misses = max(max_consecutive_misses, current_misses)
                    print(f"  {period}: {lottery_num} -> 挂 | 当前连挂: {current_misses}, 历史最大连挂: {max_consecutive_misses}")

        print(f"🎯 历史最大统计: 最大连中={max_consecutive_hits}, 最大连挂={max_consecutive_misses}")

        # 使用历史最大值
        consecutive_hits = max_consecutive_hits
        consecutive_misses = max_consecutive_misses

        # 更新统计标签
        self.hit_rate_label.SetLabel(f"命中率: {hit_rate:.1f}%")
        self.total_predictions_label.SetLabel(f"总预测: {total_predictions}次")
        self.consecutive_hits_label.SetLabel(f"最大连中: {consecutive_hits}次")
        self.consecutive_misses_label.SetLabel(f"最大连挂: {consecutive_misses}次")

        print(f"📈 统计更新: 总预测={total_predictions}, 命中率={hit_rate:.1f}%, 最大连中={consecutive_hits}, 最大连挂={consecutive_misses}")

        # 设置命中率颜色
        if hit_rate >= 70:
            self.hit_rate_label.SetForegroundColour(wx.Colour(0, 128, 0))  # 绿色
        elif hit_rate >= 50:
            self.hit_rate_label.SetForegroundColour(wx.Colour(255, 165, 0))  # 橙色
        else:
            self.hit_rate_label.SetForegroundColour(wx.Colour(255, 0, 0))  # 红色

        # 更新分析策略命中率统计
        self.update_analysis_strategy_stats()

        # 显示最新开奖号码 - 优化显示逻辑
        latest_lottery_number = None
        latest_result = None

        # 方法1：从预测记录中获取最新开奖号码
        if prediction_records:
            # 从最新记录开始查找有开奖号码的记录
            for record in reversed(prediction_records):
                if record.get('lottery_number'):
                    latest_lottery_number = record['lottery_number']
                    latest_result = record.get('result', '待验证')
                    print(f"🎯 从预测记录获取最新开奖: {latest_lottery_number} {latest_result}")
                    break

        # 方法2：如果预测记录中没有，直接从历史数据获取
        if not latest_lottery_number and hasattr(self, 'lottery_data') and self.lottery_data:
            latest_data = self.lottery_data[-1]
            latest_lottery_number = ''.join(f"{d:01d}" for d in latest_data)
            latest_result = '未验证'
            print(f"🎯 从历史数据获取最新开奖: {latest_lottery_number} (未验证)")

        # 显示最新开奖号码（只显示已验证的开奖号码）
        if latest_lottery_number and latest_result in ['中', '挂']:
            # 确保显示5位数字格式，处理各种输入格式
            if isinstance(latest_lottery_number, str):
                formatted_number = f"{latest_lottery_number:0>5}"  # 字符串格式，确保5位
            else:
                formatted_number = f"{int(latest_lottery_number):05d}"  # 整数格式，确保5位前导零

            color = wx.Colour(0, 128, 0) if latest_result == '中' else wx.Colour(255, 0, 0)
            self.latest_number_label.SetLabel(f"最新开奖: {formatted_number} {latest_result}")
            self.latest_number_label.SetForegroundColour(color)
            print(f"✅ 显示最新开奖号码: {formatted_number} {latest_result} (原始: {latest_lottery_number})")
        else:
            # 不显示未验证的开奖号码，保持空白或显示标题
            self.latest_number_label.SetLabel("最新开奖: ")
            self.latest_number_label.SetForegroundColour(wx.Colour(128, 128, 128))
            print("📭 无已验证的开奖号码，保持空白显示")

        # 填充历史记录列表（显示最近20条，只显示已验证的记录）
        recent_records = prediction_records[-20:] if len(prediction_records) > 20 else prediction_records

        # 过滤出已验证的记录（有开奖号码和结果的记录）
        verified_records = []
        for record in recent_records:
            lottery_num = record.get('lottery_number')
            result = record.get('result')
            # 只显示有开奖号码和验证结果的记录
            if lottery_num is not None and result is not None and result in ['中', '挂']:
                verified_records.append(record)

        print(f"📋 准备显示 {len(verified_records)} 条已验证历史记录")

        for i, record in enumerate(reversed(verified_records)):
            # 获取已验证的字段
            lottery_num = str(record.get('lottery_number'))
            result = str(record.get('result'))

            # 使用正确的ListCtrl方法插入项目（第一列显示开奖号码）
            index = self.main_record_list.InsertItem(i, lottery_num)

            # 第二列显示结果
            self.main_record_list.SetItem(index, 1, result)

            # 设置结果颜色
            if result == '中':
                self.main_record_list.SetItemTextColour(index, wx.Colour(0, 128, 0))
            elif result == '挂':
                self.main_record_list.SetItemTextColour(index, wx.Colour(255, 0, 0))

        print(f"✅ 预测面板更新完成！")
        
    def load_data(self):
        """加载彩票数据"""
        try:
            if not os.path.exists(self.data_file_path):
                # 如果文件不存在，创建示例数据
                self.create_sample_data()

            # 记录文件修改时间
            self.last_file_mtime = os.path.getmtime(self.data_file_path)

            with open(self.data_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            self.lottery_data = []
            for line in lines:
                line = line.strip()
                if line:
                    # 优先尝试标准格式：时间戳 + 5位数字
                    match = re.match(r'(\d{12})\s+(\d{5})', line)
                    if match:
                        timestamp_str = match.group(1)
                        number_str = match.group(2)

                        # 验证时间戳格式（分钟必须小于60）
                        try:
                            minute = int(timestamp_str[10:12])
                            if minute < 60:
                                # 有效的时间戳和号码
                                digits = [int(d) for d in number_str]
                                self.lottery_data.append(digits)
                                continue
                        except ValueError:
                            pass

                    # 如果标准格式失败，回退到原始方法
                    numbers = re.findall(r'\d', line)
                    if len(numbers) >= 5:
                        # 取最后5位数字
                        last_5_digits = numbers[-5:]
                        self.lottery_data.append([int(d) for d in last_5_digits])

            # 修正数据顺序：文件中第一行是最新数据，需要反转列表
            # 确保self.lottery_data中索引0是最早期，索引-1是最新期
            if self.lottery_data:
                self.lottery_data.reverse()

            # 更新数据信息显示
            current_time = time.strftime("%H:%M:%S")

            # 构建显示文本，包含最新一期号码信息
            if self.lottery_data:
                # 获取最新一期的5位数字并转换为字符串（现在最新数据在列表末尾）
                latest_numbers = self.lottery_data[-1]
                latest_number_str = ''.join(map(str, latest_numbers))
                info_text = f"已加载 {len(self.lottery_data)} 期历史数据，最新期：{latest_number_str} (更新时间: {current_time})"
            else:
                info_text = f"暂无历史数据 (更新时间: {current_time})"

            self.data_info_label.SetLabel(info_text)

            # 执行统计分析
            self.analyze_data()

            # 更新预测面板显示
            self.update_main_prediction_display()
            print(f"🔄 数据加载完成，已更新预测面板显示")

        except Exception as e:
            wx.MessageBox(f"加载数据失败: {str(e)}", "错误", wx.OK | wx.ICON_ERROR)

    def update_analysis_strategy_stats(self):
        """更新分析策略命中率统计"""
        if not hasattr(self, 'filter_component'):
            return

        # 获取数字连续未出现分析策略统计
        digit_absence_strategy = self.filter_component.filter_strategies.get('digit_absence_filter', {})
        digit_absence_total = digit_absence_strategy.get('total_predictions', 0)
        digit_absence_correct = digit_absence_strategy.get('correct_predictions', 0)
        digit_absence_rate = (digit_absence_correct / digit_absence_total * 100) if digit_absence_total > 0 else 0.0

        # 获取未出现最少分析策略统计
        latest_absence_strategy = self.filter_component.filter_strategies.get('latest_absence_filter', {})
        latest_absence_total = latest_absence_strategy.get('total_predictions', 0)
        latest_absence_correct = latest_absence_strategy.get('correct_predictions', 0)
        latest_absence_rate = (latest_absence_correct / latest_absence_total * 100) if latest_absence_total > 0 else 0.0

        # 更新UI显示
        if hasattr(self, 'digit_absence_stats_label'):
            self.digit_absence_stats_label.SetLabel(f"数字连续未出现分析: {digit_absence_correct}/{digit_absence_total} ({digit_absence_rate:.1f}%)")
            # 设置颜色
            if digit_absence_rate >= 70:
                self.digit_absence_stats_label.SetForegroundColour(wx.Colour(0, 128, 0))  # 绿色
            elif digit_absence_rate >= 50:
                self.digit_absence_stats_label.SetForegroundColour(wx.Colour(255, 165, 0))  # 橙色
            else:
                self.digit_absence_stats_label.SetForegroundColour(wx.Colour(255, 0, 0))  # 红色

        if hasattr(self, 'latest_absence_stats_label'):
            self.latest_absence_stats_label.SetLabel(f"未出现最少分析: {latest_absence_correct}/{latest_absence_total} ({latest_absence_rate:.1f}%)")
            # 设置颜色
            if latest_absence_rate >= 70:
                self.latest_absence_stats_label.SetForegroundColour(wx.Colour(0, 128, 0))  # 绿色
            elif latest_absence_rate >= 50:
                self.latest_absence_stats_label.SetForegroundColour(wx.Colour(255, 165, 0))  # 橙色
            else:
                self.latest_absence_stats_label.SetForegroundColour(wx.Colour(255, 0, 0))  # 红色

        print(f"📊 分析策略统计更新: 数字连续未出现={digit_absence_correct}/{digit_absence_total}({digit_absence_rate:.1f}%), 未出现最少={latest_absence_correct}/{latest_absence_total}({latest_absence_rate:.1f}%)")

    def create_sample_data(self):
        """创建示例数据文件"""
        import random
        sample_data = []

        # 生成50期示例数据，使数据更有统计意义
        for i in range(1, 51):
            # 生成随机的5位数字
            digits = [random.randint(0, 9) for _ in range(5)]
            number_str = ''.join(map(str, digits))
            sample_data.append(f"2024{i:03d}期 开奖号码: {number_str}")

        # 确保目录存在
        os.makedirs(os.path.dirname(self.data_file_path), exist_ok=True)

        with open(self.data_file_path, 'w', encoding='utf-8') as f:
            for line in sample_data:
                f.write(line + '\n')
                
    def analyze_data(self):
        """执行数据分析"""
        if not self.lottery_data:
            return

        # 1. 和值统计
        self.analyze_sum_values()

        # 2. 位置数字统计
        self.analyze_position_digits()

        # 3. 大小组合统计
        self.analyze_size_combinations()

        # 4. 奇偶组合统计
        self.analyze_parity_combinations()

        # 5. 数字连续未出现分析
        self.analyze_digit_absence()

        # 6. 最新未出现最少分析
        self.analyze_latest_absence()

        # 7. 更新筛选策略信息
        if hasattr(self, 'filter_component'):
            self.filter_component.update_strategy_info()

        # 6. 检查预测命中情况（如果有新数据）
        if self.lottery_data and hasattr(self, 'filter_component'):
            latest_data = self.lottery_data[-1]  # 最新的开奖数据（数字列表）
            # 将数字列表转换为5位数字，保持字符串格式以保留前导零
            latest_number_str = ''.join(f"{d:01d}" for d in latest_data)  # 确保每位都是字符串
            latest_number_int = int(latest_number_str)  # 用于比较的整数格式

            print(f"🔍 最新开奖数据: {latest_data} -> 字符串: {latest_number_str} -> 整数: {latest_number_int}")

            # 只有当开奖号码与上次检查的不同时才进行检查
            if latest_number_int != self.filter_component.last_checked_number:
                # 提取期号信息
                period_info = f"第{len(self.lottery_data)}期"
                # 检查命中情况，传递字符串格式的号码
                self.filter_component.check_prediction_hits(latest_number_str, period_info)
                # 更新上次检查的号码（使用整数格式进行比较）
                self.filter_component.last_checked_number = latest_number_int

        # 6. 自动执行层层筛选
        self.execute_layer_filter()
        
    def analyze_sum_values(self):
        """分析和值统计"""
        self.sum_list.DeleteAllItems()
        
        # 计算每期的和值
        sum_history = []
        for data in self.lottery_data:
            sum_value = sum(data)
            sum_history.append(sum_value)
        
        # 统计每个和值距离最新一期的未出现期数
        sum_periods = {}
        for period, sum_val in enumerate(reversed(sum_history)):
            if sum_val not in sum_periods:
                sum_periods[sum_val] = period
        
        # 为所有可能的和值（0-45）计算未出现期数
        all_sums = {}
        for sum_val in range(46):  # 0到45
            if sum_val in sum_periods:
                all_sums[sum_val] = sum_periods[sum_val]
            else:
                all_sums[sum_val] = len(sum_history)  # 从未出现
        
        # 按未出现期数从多到少排序
        sorted_sums = sorted(all_sums.items(), key=lambda x: x[1], reverse=True)
        
        # 显示结果（正确的序号格式）
        for i, (sum_val, periods) in enumerate(sorted_sums):
            # 第一列：纯数字序号
            index = self.sum_list.InsertItem(i, str(i+1))
            # 第二列：和值
            self.sum_list.SetItem(index, 1, str(sum_val))
            # 第三列：未出现期数
            self.sum_list.SetItem(index, 2, str(periods))
            # 第四列：概率
            probability = min(100, (periods + 1) * 2)  # 简单的概率计算
            self.sum_list.SetItem(index, 3, f"{probability}%")
            
    def analyze_position_digits(self):
        """分析位置数字统计"""
        self.position_list.DeleteAllItems()
        
        # 统计每个位置每个数字的最后出现期数
        position_digit_periods = {}
        
        for period, data in enumerate(reversed(self.lottery_data)):
            for pos in range(5):
                digit = data[pos]
                key = f"第{pos+1}位-{digit}"
                if key not in position_digit_periods:
                    position_digit_periods[key] = period
        
        # 为所有可能的位置-数字组合计算未出现期数
        all_position_digits = {}
        for pos in range(5):
            for digit in range(10):
                key = f"第{pos+1}位-{digit}"
                if key in position_digit_periods:
                    all_position_digits[key] = position_digit_periods[key]
                else:
                    all_position_digits[key] = len(self.lottery_data)
        
        # 按未出现期数从多到少排序
        sorted_position_digits = sorted(all_position_digits.items(), key=lambda x: x[1], reverse=True)
        
        # 显示结果（正确的序号格式）
        for i, (pos_digit, periods) in enumerate(sorted_position_digits):
            # 第一列：纯数字序号
            index = self.position_list.InsertItem(i, str(i+1))
            # 第二列：位置-数字
            self.position_list.SetItem(index, 1, pos_digit)
            # 第三列：未出现期数
            self.position_list.SetItem(index, 2, str(periods))
            # 第四列：概率
            probability = min(100, (periods + 1) * 2)
            self.position_list.SetItem(index, 3, f"{probability}%")
            
    def analyze_size_combinations(self):
        """分析大小组合统计"""
        self.size_list.DeleteAllItems()
        
        # 定义大小分类：0-4为小，5-9为大
        def get_size_pattern(data):
            pattern = ""
            for digit in data:
                if digit <= 4:
                    pattern += "小"
                else:
                    pattern += "大"
            return pattern
        
        # 统计每种大小组合的最后出现期数
        size_pattern_periods = {}
        
        for period, data in enumerate(reversed(self.lottery_data)):
            pattern = get_size_pattern(data)
            if pattern not in size_pattern_periods:
                size_pattern_periods[pattern] = period
        
        # 生成所有可能的大小组合（2^5 = 32种）
        all_size_patterns = {}
        for combination in product(['小', '大'], repeat=5):
            pattern = ''.join(combination)
            if pattern in size_pattern_periods:
                all_size_patterns[pattern] = size_pattern_periods[pattern]
            else:
                all_size_patterns[pattern] = len(self.lottery_data)
        
        # 按未出现期数从多到少排序
        sorted_size_patterns = sorted(all_size_patterns.items(), key=lambda x: x[1], reverse=True)
        
        # 显示结果（正确的序号格式）
        for i, (pattern, periods) in enumerate(sorted_size_patterns):
            # 第一列：纯数字序号
            index = self.size_list.InsertItem(i, str(i+1))
            # 第二列：大小组合
            self.size_list.SetItem(index, 1, pattern)
            # 第三列：未出现期数
            self.size_list.SetItem(index, 2, str(periods))
            # 第四列：概率
            probability = min(100, (periods + 1) * 3)  # 组合概率稍高
            self.size_list.SetItem(index, 3, f"{probability}%")
            
    def analyze_parity_combinations(self):
        """分析奇偶组合统计"""
        self.parity_list.DeleteAllItems()
        
        # 定义奇偶分类
        def get_parity_pattern(data):
            pattern = ""
            for digit in data:
                if digit % 2 == 0:
                    pattern += "偶"
                else:
                    pattern += "奇"
            return pattern
        
        # 统计每种奇偶组合的最后出现期数
        parity_pattern_periods = {}
        
        for period, data in enumerate(reversed(self.lottery_data)):
            pattern = get_parity_pattern(data)
            if pattern not in parity_pattern_periods:
                parity_pattern_periods[pattern] = period
        
        # 生成所有可能的奇偶组合（2^5 = 32种）
        all_parity_patterns = {}
        for combination in product(['偶', '奇'], repeat=5):
            pattern = ''.join(combination)
            if pattern in parity_pattern_periods:
                all_parity_patterns[pattern] = parity_pattern_periods[pattern]
            else:
                all_parity_patterns[pattern] = len(self.lottery_data)
        
        # 按未出现期数从多到少排序
        sorted_parity_patterns = sorted(all_parity_patterns.items(), key=lambda x: x[1], reverse=True)
        
        # 显示结果（正确的序号格式）
        for i, (pattern, periods) in enumerate(sorted_parity_patterns):
            # 第一列：纯数字序号
            index = self.parity_list.InsertItem(i, str(i+1))
            # 第二列：奇偶组合
            self.parity_list.SetItem(index, 1, pattern)
            # 第三列：未出现期数
            self.parity_list.SetItem(index, 2, str(periods))
            # 第四列：概率
            probability = min(100, (periods + 1) * 3)  # 组合概率稍高
            self.parity_list.SetItem(index, 3, f"{probability}%")

    def analyze_digit_absence(self):
        """分析数字出现频率和连续未出现情况"""
        if not self.lottery_data or not hasattr(self, 'filter_component'):
            return

        # 获取策略配置
        strategy = self.filter_component.filter_strategies.get('digit_absence_filter')
        if not strategy or not strategy['enabled']:
            return

        # 同步UI控件的值到策略配置（确保最新值）
        if hasattr(self.filter_component, 'strategy_controls') and 'digit_absence_filter' in self.filter_component.strategy_controls:
            controls = self.filter_component.strategy_controls['digit_absence_filter']
            if 'prediction_count_spin' in controls:
                ui_prediction_count = controls['prediction_count_spin'].GetValue()
                strategy['prediction_count'] = ui_prediction_count

        periods_to_analyze = strategy['count']

        # 分析最近指定期数的数据 - 用于预测
        recent_data = self.lottery_data[-periods_to_analyze:] if len(self.lottery_data) >= periods_to_analyze else self.lottery_data

        # 1. 统计最近指定期数内每个数字的出现次数（用于预测）
        digit_frequency = {}
        for digit in range(10):
            digit_frequency[digit] = 0

        for period_data in recent_data:
            for digit in period_data:
                if digit in digit_frequency:
                    digit_frequency[digit] += 1

        # 获取预测数量配置
        prediction_count = strategy.get('prediction_count', 1)

        # 按出现次数排序，选择前N个最多的数字
        sorted_digits = sorted(digit_frequency.items(), key=lambda x: x[1], reverse=True)
        predicted_digits = [digit for digit, count in sorted_digits[:prediction_count]]

        # 保持向后兼容
        most_frequent_digit = predicted_digits[0] if predicted_digits else 0
        max_frequency = digit_frequency[most_frequent_digit]

        # 2. 统计全部历史数据中每个数字的最大连续未出现次数（用于展示）
        digit_absence_counts = {}

        for digit in range(10):
            max_consecutive_absence = 0
            current_consecutive_absence = 0

            # 从最新期开始往前分析全部历史数据
            for period_data in reversed(self.lottery_data):
                # 检查当前期的5位数字中是否包含该数字
                if digit in period_data:
                    # 数字出现了，重置连续未出现计数
                    max_consecutive_absence = max(max_consecutive_absence, current_consecutive_absence)
                    current_consecutive_absence = 0
                else:
                    # 数字未出现，增加连续未出现计数
                    current_consecutive_absence += 1

            # 处理到最后一期仍未出现的情况
            max_consecutive_absence = max(max_consecutive_absence, current_consecutive_absence)
            digit_absence_counts[digit] = max_consecutive_absence

        # 更新策略配置中的预测数字和统计信息
        strategy['predicted_digits'] = predicted_digits
        strategy['predicted_digit'] = most_frequent_digit  # 保持向后兼容
        strategy['digit_absence_counts'] = digit_absence_counts
        strategy['digit_frequency'] = digit_frequency
        strategy['periods_analyzed'] = periods_to_analyze

        # 生成统计信息文本并存储
        stats_text = self.generate_digit_absence_statistics_text(digit_absence_counts, digit_frequency, periods_to_analyze, predicted_digits)
        strategy['statistics_text'] = stats_text

        print(f"📊 统计信息已生成，长度: {len(stats_text)} 字符")
        print(f"📊 预测数字: {predicted_digits} (预测数量: {prediction_count})")
        print(f"📊 策略配置已更新: {strategy.get('predicted_digit', '未设置')}")

        # 数字连续最多策略不再显示UI控件，仅保留数据更新

        print(f"🔍 数字出现频率分析 (最近{periods_to_analyze}期):")
        print(f"   各数字出现次数: {digit_frequency}")
        print(f"   预测数字: {predicted_digits} (预测数量: {prediction_count})")
        print(f"🔍 全部历史数据连续未出现分析:")
        print(f"   各数字最大连续未出现次数: {digit_absence_counts}")

    def analyze_latest_absence(self):
        """分析最新未出现最少的数字"""
        if not self.lottery_data or not hasattr(self, 'filter_component'):
            return

        # 获取策略配置
        strategy = self.filter_component.filter_strategies.get('latest_absence_filter')
        if not strategy or not strategy['enabled']:
            return

        # 同步UI控件的值到策略配置（确保最新值）
        if hasattr(self.filter_component, 'strategy_controls') and 'latest_absence_filter' in self.filter_component.strategy_controls:
            controls = self.filter_component.strategy_controls['latest_absence_filter']
            if 'prediction_count_spin' in controls:
                ui_prediction_count = controls['prediction_count_spin'].GetValue()
                strategy['prediction_count'] = ui_prediction_count

        analysis_range = strategy['count']

        # 分析指定范围内的数据，如果范围大于等于历史数据总数，则分析所有数据
        if analysis_range >= len(self.lottery_data):
            analysis_data = self.lottery_data
            actual_range = len(self.lottery_data)
            range_description = f"所有{actual_range}期"
        else:
            analysis_data = self.lottery_data[-analysis_range:]
            actual_range = analysis_range
            range_description = f"最近{actual_range}期"

        # 统计每个数字在历史数据中的最大连续未出现次数
        max_absence_counts = {}

        for digit in range(10):
            max_consecutive_absence = 0
            current_consecutive_absence = 0

            # 遍历历史数据，统计最大连续未出现次数
            for period_data in analysis_data:
                if digit in period_data:
                    # 数字出现，重置当前连续未出现计数
                    max_consecutive_absence = max(max_consecutive_absence, current_consecutive_absence)
                    current_consecutive_absence = 0
                else:
                    # 数字未出现，增加当前连续未出现计数
                    current_consecutive_absence += 1

            # 检查最后一段连续未出现的情况
            max_consecutive_absence = max(max_consecutive_absence, current_consecutive_absence)
            max_absence_counts[digit] = max_consecutive_absence

        # 获取预测数量配置
        prediction_count = strategy.get('prediction_count', 1)

        # 按历史最大连续未出现次数排序，选择前N个最少的数字
        sorted_digits = sorted(max_absence_counts.items(), key=lambda x: x[1])
        predicted_digits = [digit for digit, count in sorted_digits[:prediction_count]]

        # 生成统计信息文本
        stats_text = self.generate_latest_absence_statistics_text(max_absence_counts, actual_range, predicted_digits, range_description)

        # 更新策略配置中的预测数字和统计信息
        strategy['predicted_digits'] = predicted_digits
        strategy['predicted_digit'] = predicted_digits[0] if predicted_digits else None  # 保持向后兼容
        strategy['max_absence_counts'] = max_absence_counts
        strategy['analysis_range'] = actual_range
        strategy['range_description'] = range_description
        strategy['statistics_text'] = stats_text

        # 未出现最少分析策略不再显示UI控件，仅保留数据更新

        print(f"🔍 最新未出现最少分析 ({range_description}):")
        print(f"   各数字历史最大连续未出现次数: {max_absence_counts}")
        print(f"   预测数字: {predicted_digits} (预测数量: {prediction_count})")

    def analyze_kill_previous_pattern(self):
        """分析杀上期形态策略"""
        if not self.lottery_data or not hasattr(self, 'filter_component'):
            return

        # 获取策略配置
        strategy = self.filter_component.filter_strategies.get('kill_previous_pattern_filter')
        if not strategy or not strategy['enabled']:
            return

        pattern_type = strategy.get('pattern_type', 'sum')

        print(f"🔍 执行杀上期形态分析 (形态类型: {pattern_type})...")

        # 计算历史形态数据
        pattern_history = []
        print(f"🔍 开始计算历史形态数据，总期数: {len(self.lottery_data)}")

        for i, lottery_number in enumerate(self.lottery_data):
            pattern_value = self.calculate_pattern_value(lottery_number, pattern_type)
            pattern_history.append(pattern_value)

            # 显示前5期的详细信息用于调试
            if i < 5:
                print(f"🔍 第{i+1}期: 开奖号码={lottery_number}, 形态值={pattern_value}")

        if len(pattern_history) < 2:
            print("❌ 历史数据不足，无法进行杀上期形态分析")
            return

        # 计算连续未重复次数
        consecutive_count = 0
        current_pattern = pattern_history[0]  # 最新一期的形态值

        print(f"🔍 最新一期形态值: {current_pattern} (来源: {self.lottery_data[0]})")

        # 从第二期开始向后比较
        for i in range(1, len(pattern_history)):
            if pattern_history[i] != pattern_history[i-1]:
                consecutive_count += 1
            else:
                break  # 遇到重复就停止计数

        # 统计形态值出现频率
        pattern_frequency = {}
        for pattern_value in pattern_history:
            pattern_frequency[pattern_value] = pattern_frequency.get(pattern_value, 0) + 1

        # 计算重复概率预测
        total_periods = len(pattern_history)
        repeat_probability = self.calculate_repeat_probability(consecutive_count, pattern_frequency, current_pattern, total_periods)

        # 检查是否需要进行筛选
        enable_filtering = strategy.get('enable_filtering', False)
        filter_threshold = strategy.get('filter_threshold', 10)
        excluded_pattern_value = None

        if enable_filtering and pattern_type == 'sum' and consecutive_count > filter_threshold:
            excluded_pattern_value = current_pattern
            print(f"🚫 筛选条件满足: 和值{current_pattern}连续{consecutive_count}期未重复 > 阈值{filter_threshold}期，将被筛选")

        # 更新策略配置
        strategy['consecutive_count'] = consecutive_count
        strategy['current_pattern'] = current_pattern
        strategy['pattern_history'] = pattern_history[:50]  # 只保存最近50期
        strategy['pattern_frequency'] = pattern_frequency
        strategy['repeat_probability'] = repeat_probability
        strategy['excluded_pattern_value'] = excluded_pattern_value

        # 生成统计信息文本
        stats_text = self.generate_kill_previous_pattern_statistics_text(
            pattern_type, current_pattern, consecutive_count, pattern_frequency, repeat_probability, total_periods, enable_filtering, filter_threshold, excluded_pattern_value
        )
        strategy['statistics_text'] = stats_text

        print(f"🔍 杀上期形态分析结果:")
        print(f"   形态类型: {self.get_pattern_type_name(pattern_type)}")
        print(f"   当前形态值: {current_pattern}")
        print(f"   连续未重复次数: {consecutive_count}")
        print(f"   重复概率预测: {repeat_probability:.1f}%")
        if excluded_pattern_value is not None:
            print(f"   筛选状态: 和值{excluded_pattern_value}将被排除")

    def calculate_pattern_value(self, lottery_number, pattern_type):
        """计算指定形态类型的值"""
        # 将开奖号码转换为数字列表（支持5位或6位）
        if isinstance(lottery_number, list):
            # 如果已经是列表，直接使用
            digits = lottery_number
        elif isinstance(lottery_number, (int, str)):
            # 如果是整数或字符串，转换为数字列表
            lottery_str = str(lottery_number)
            digits = [int(d) for d in lottery_str if d.isdigit()]

            # 调试输出
            print(f"🔍 形态值计算: 输入={lottery_number}, 数字列表={digits}, 长度={len(digits)}")

        else:
            # 其他类型，尝试转换为字符串再处理
            lottery_str = str(lottery_number)
            digits = [int(d) for d in lottery_str if d.isdigit()]

            print(f"🔍 形态值计算(其他类型): 输入={lottery_number}, 数字列表={digits}, 长度={len(digits)}")

        # 移除位数限制，支持任意位数的彩票号码

        if pattern_type == 'sum':
            # 和值
            return sum(digits)
        elif pattern_type == 'parity':
            # 奇偶组合 (用字符串表示，如"奇偶奇偶奇")
            parity_pattern = ''.join(['奇' if d % 2 == 1 else '偶' for d in digits])
            return parity_pattern
        elif pattern_type == 'size':
            # 大小组合 (用字符串表示，如"大小大小大")
            size_pattern = ''.join(['大' if d >= 5 else '小' for d in digits])
            return size_pattern
        elif pattern_type == 'span':
            # 跨度 (最大值-最小值)
            return max(digits) - min(digits)
        elif pattern_type == 'ac':
            # AC值 (不同数字的个数-1)
            return len(set(digits)) - 1
        else:
            return sum(digits)  # 默认返回和值

    def calculate_repeat_probability(self, consecutive_count, pattern_frequency, current_pattern, total_periods):
        """计算重复概率"""
        # 基础重复概率（基于历史频率）
        current_frequency = pattern_frequency.get(current_pattern, 0)
        base_probability = (current_frequency / total_periods) * 100 if total_periods > 0 else 0

        # 连续未重复次数调整因子
        # 连续次数越多，下期重复的概率越高
        consecutive_factor = min(consecutive_count * 5, 50)  # 最多增加50%

        # 最终概率
        final_probability = min(base_probability + consecutive_factor, 95)  # 最高95%

        return final_probability

    def get_pattern_type_name(self, pattern_type):
        """获取形态类型的中文名称"""
        pattern_names = {
            'sum': '和值',
            'parity': '奇偶组合',
            'size': '大小组合',
            'span': '跨度',
            'ac': 'AC值'
        }
        return pattern_names.get(pattern_type, '和值')

    def generate_kill_previous_pattern_statistics_text(self, pattern_type, current_pattern, consecutive_count, pattern_frequency, repeat_probability, total_periods, enable_filtering=False, filter_threshold=10, excluded_pattern_value=None):
        """生成杀上期形态统计信息文本"""
        stats_lines = []
        stats_lines.append("=" * 50)
        stats_lines.append("🎯 杀上期形态分析")
        stats_lines.append("=" * 50)

        # 显示当前分析信息
        pattern_name = self.get_pattern_type_name(pattern_type)
        stats_lines.append(f"📊 分析形态: {pattern_name}")
        stats_lines.append(f"📊 当前形态值: {current_pattern}")
        stats_lines.append(f"📊 连续未重复次数: {consecutive_count}")
        stats_lines.append(f"📊 重复概率预测: {repeat_probability:.1f}%")

        # 显示筛选信息
        if enable_filtering and pattern_type == 'sum':
            stats_lines.append(f"🚫 筛选功能: 已启用 (阈值: {filter_threshold}期)")
            if excluded_pattern_value is not None:
                stats_lines.append(f"🚫 筛选状态: 和值{excluded_pattern_value}将被排除")
            else:
                stats_lines.append(f"✅ 筛选状态: 当前和值未达到筛选条件")
        else:
            stats_lines.append(f"⚪ 筛选功能: 未启用")

        # 显示预测建议
        if repeat_probability >= 70:
            suggestion = "高概率重复，建议杀上期形态"
        elif repeat_probability >= 40:
            suggestion = "中等概率重复，谨慎考虑"
        else:
            suggestion = "低概率重复，可能出现新形态"

        stats_lines.append(f"💡 预测建议: {suggestion}")

        # 显示形态频率统计（前10个最常见的）
        stats_lines.append(f"\n📈 形态频率统计 (总计{total_periods}期):")
        sorted_frequency = sorted(pattern_frequency.items(), key=lambda x: x[1], reverse=True)
        for i, (pattern_value, count) in enumerate(sorted_frequency[:10]):
            percentage = (count / total_periods) * 100
            marker = " ← 当前" if pattern_value == current_pattern else ""
            stats_lines.append(f"   {pattern_value}: {count}次 ({percentage:.1f}%){marker}")

        # 显示连续未重复分析
        stats_lines.append(f"\n🔍 连续未重复分析:")
        if consecutive_count == 0:
            stats_lines.append("   上期形态已重复，当前为新的计数周期")
        elif consecutive_count <= 3:
            stats_lines.append(f"   连续{consecutive_count}期未重复，重复概率较低")
        elif consecutive_count <= 6:
            stats_lines.append(f"   连续{consecutive_count}期未重复，重复概率中等")
        else:
            stats_lines.append(f"   连续{consecutive_count}期未重复，重复概率较高")

        stats_lines.append("=" * 50)

        return "\n".join(stats_lines)

    def generate_latest_absence_statistics_text(self, max_absence_counts, actual_range, predicted_digits, range_description):
        """生成最新未出现最少分析的统计信息文本"""
        stats_lines = []
        stats_lines.append("=" * 50)
        stats_lines.append("📊 未出现最少统计分析")
        stats_lines.append("=" * 50)

        # 显示预测信息
        stats_lines.append(f"🎯 预测结果 (基于{range_description}数据):")
        if isinstance(predicted_digits, list):
            stats_lines.append(f"   预测数字: {predicted_digits} (共{len(predicted_digits)}个)")
        else:
            stats_lines.append(f"   预测数字: {predicted_digits}")

        # 添加说明文字
        stats_lines.append(f"\n📝 说明:")
        stats_lines.append(f"   统计各数字在历史数据中的最大连续未出现次数")
        stats_lines.append(f"   选择历史最大连续未出现次数最少的数字作为预测")

        # 显示各数字历史最大连续未出现次数（按次数升序排列）
        stats_lines.append(f"\n📈 各数字历史最大连续未出现次数（按次数升序排列）:")
        # 按最大连续未出现次数从少到多排序
        sorted_absence = sorted(max_absence_counts.items(), key=lambda x: x[1])
        for digit, count in sorted_absence:
            stats_lines.append(f"   数字{digit}: {count}期")
        stats_lines.append("=" * 50)

        return "\n".join(stats_lines)

    def generate_digit_absence_statistics_text(self, digit_absence_counts, digit_frequency, periods_analyzed, predicted_digits):
        """生成数字连续未出现统计信息文本"""
        stats_lines = []
        #stats_lines.append("=" * 50)
        #stats_lines.append("数字连续未出现统计分析")
        stats_lines.append("=" * 50)

        # 显示预测信息
        stats_lines.append(f" 预测结果 (基于最近{periods_analyzed}期数据):")
        if isinstance(predicted_digits, list):
            stats_lines.append(f"   预测数字: {predicted_digits} (共{len(predicted_digits)}个)")
        else:
            stats_lines.append(f"   预测数字: {predicted_digits}")

        # 显示最近期数的出现频率（按出现次数降序排列）
        stats_lines.append(f"\n最近{periods_analyzed}期各数字出现次数（按出现次数降序排列）:")
        # 按出现次数从多到少排序
        sorted_frequency = sorted(digit_frequency.items(), key=lambda x: x[1], reverse=True)
        for digit, count in sorted_frequency:
            stats_lines.append(f"   数字{digit}: {count}次")
        stats_lines.append("=" * 50)

        # 显示全部历史数据的连续未出现统计（按连续未出现次数降序排列）
        stats_lines.append(f"\n 全部历史数据中各数字最大连续未出现次数（按连续未出现次数降序排列）:")
        # 按连续未出现次数从多到少排序
        sorted_absence = sorted(digit_absence_counts.items(), key=lambda x: x[1], reverse=True)
        for digit, count in sorted_absence:
            stats_lines.append(f"   数字{digit}: {count}期")
        stats_lines.append("=" * 50)

        return "\n".join(stats_lines)

    def display_digit_absence_statistics(self, digit_absence_counts, digit_frequency, periods_analyzed, predicted_digit):
        """在筛选结果区域显示数字连续未出现统计信息"""
        if not hasattr(self, 'filter_component'):
            return

        # 构建统计信息文本
        stats_lines = []
        stats_lines.append("=" * 50)
        stats_lines.append(" 数字连续未出现统计分析")
        stats_lines.append("=" * 50)

        # 显示预测信息
        stats_lines.append(f" 预测结果 (基于最近{periods_analyzed}期数据):")
        stats_lines.append(f"   预测数字: {predicted_digit}")

        # 显示最近期数的出现频率
        stats_lines.append(f"\n 最近{periods_analyzed}期各数字出现次数:")
        frequency_text = ", ".join([f"数字{digit}: {count}次" for digit, count in sorted(digit_frequency.items())])
        stats_lines.append(f"   {frequency_text}")

        # 显示全部历史数据的连续未出现统计
        stats_lines.append(f"\n全部历史数据中各数字最大连续未出现次数:")
        absence_text = ", ".join([f"数字{digit}: {count}期" for digit, count in sorted(digit_absence_counts.items())])
        stats_lines.append(f"   {absence_text}")

        stats_lines.append("=" * 50)

        # 将统计信息添加到筛选结果显示区域
        stats_text = "\n".join(stats_lines)

        # 如果筛选组件存在，将统计信息添加到筛选过程文本中
        if hasattr(self.filter_component, 'filter_process_text'):
            current_text = self.filter_component.filter_process_text.GetValue()
            if current_text:
                updated_text = current_text + "\n\n" + stats_text
            else:
                updated_text = stats_text
            self.filter_component.filter_process_text.SetValue(updated_text)

            # 滚动到底部显示最新内容
            self.filter_component.filter_process_text.SetInsertionPointEnd()

    def on_browse_file(self, event):
        """浏览文件按钮事件"""
        wildcard = "文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*"
        dialog = wx.FileDialog(self, "选择彩票数据文件", wildcard=wildcard, style=wx.FD_OPEN)

        if dialog.ShowModal() == wx.ID_OK:
            self.data_file_path = dialog.GetPath()
            self.file_path_text.SetValue(self.data_file_path)
            self.load_data()

        dialog.Destroy()

    def on_refresh_data(self, event):
        """刷新数据按钮事件 - 静默刷新，无弹出提示"""
        self.load_data()
        # 移除弹出提示框，改为静默刷新提升用户体验

    def on_export_results(self, event):
        """导出统计结果"""
        if not self.lottery_data:
            wx.MessageBox("没有数据可导出", "提示", wx.OK | wx.ICON_WARNING)
            return

        wildcard = "文本文件 (*.txt)|*.txt"
        dialog = wx.FileDialog(self, "保存统计结果", wildcard=wildcard, style=wx.FD_SAVE | wx.FD_OVERWRITE_PROMPT)

        if dialog.ShowModal() == wx.ID_OK:
            try:
                filepath = dialog.GetPath()
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write("彩票数据统计分析结果\n")
                    f.write("=" * 50 + "\n\n")
                    f.write(f"数据文件: {self.data_file_path}\n")
                    f.write(f"分析期数: {len(self.lottery_data)}期\n\n")

                    # 导出和值统计
                    f.write("和值统计（按未出现期数排序）:\n")
                    f.write("-" * 30 + "\n")
                    for i in range(self.sum_list.GetItemCount()):
                        sum_val = self.sum_list.GetItemText(i, 0)
                        periods = self.sum_list.GetItemText(i, 1)
                        prob = self.sum_list.GetItemText(i, 2)
                        f.write(f"和值{sum_val}: {periods}期未出现, 概率{prob}\n")
                    f.write("\n")

                    # 导出位置数字统计（只显示前20个）
                    f.write("位置数字统计（前20个）:\n")
                    f.write("-" * 30 + "\n")
                    for i in range(min(20, self.position_list.GetItemCount())):
                        pos_digit = self.position_list.GetItemText(i, 0)
                        periods = self.position_list.GetItemText(i, 1)
                        prob = self.position_list.GetItemText(i, 2)
                        f.write(f"{pos_digit}: {periods}期未出现, 概率{prob}\n")
                    f.write("\n")

                    # 导出大小组合统计（前10个）
                    f.write("大小组合统计（前10个）:\n")
                    f.write("-" * 30 + "\n")
                    for i in range(min(10, self.size_list.GetItemCount())):
                        pattern = self.size_list.GetItemText(i, 0)
                        periods = self.size_list.GetItemText(i, 1)
                        prob = self.size_list.GetItemText(i, 2)
                        f.write(f"{pattern}: {periods}期未出现, 概率{prob}\n")
                    f.write("\n")

                    # 导出奇偶组合统计（前10个）
                    f.write("奇偶组合统计（前10个）:\n")
                    f.write("-" * 30 + "\n")
                    for i in range(min(10, self.parity_list.GetItemCount())):
                        pattern = self.parity_list.GetItemText(i, 0)
                        periods = self.parity_list.GetItemText(i, 1)
                        prob = self.parity_list.GetItemText(i, 2)
                        f.write(f"{pattern}: {periods}期未出现, 概率{prob}\n")

                wx.MessageBox(f"统计结果已导出到: {filepath}", "导出成功", wx.OK | wx.ICON_INFORMATION)
            except Exception as e:
                wx.MessageBox(f"导出失败: {str(e)}", "错误", wx.OK | wx.ICON_ERROR)

        dialog.Destroy()



    def execute_layer_filter(self):
        """执行层层筛选 - 根据用户选择的策略进行筛选（性能优化版）"""
        import time
        start_time = time.time()
        print("🔄 开始执行层层筛选...")

        try:
            if not self.lottery_data:
                # 通过组件显示错误信息
                error_msg = ["错误：没有历史数据，无法执行筛选"]
                print("❌ 没有历史数据")
                self.filter_component.set_filter_data([], error_msg, 0)
                return

            print(f"📊 历史数据期数: {len(self.lottery_data)}")

            # 初始化10万注数据 - 性能优化：预计算数字分解
            print("⚡ 预计算数字分解...")
            precompute_start = time.time()
            all_numbers = set(range(100000))  # 00000-99999

            # 预计算所有数字的分解结果，避免重复计算
            self.number_cache = {}
            # 进度条已禁用，仅保留状态消息
            self.filter_component.update_progress(10, "预计算数字分解...")

            for i, num in enumerate(range(100000)):
                digits = [int(d) for d in f"{num:05d}"]
                self.number_cache[num] = {
                    'digits': digits,
                    'sum': sum(digits),
                    'large_count': sum(1 for d in digits if d >= 5),
                    'odd_count': sum(1 for d in digits if d % 2 == 1)
                }

                # 进度条已禁用，仅保留关键节点的状态更新
                if i % 25000 == 0:  # 减少状态更新频率
                    self.filter_component.update_progress(0, f"预计算进度: {i//1000}k/100k")

            precompute_time = time.time() - precompute_start
            print(f"⚡ 预计算完成，耗时: {precompute_time:.2f}秒")

            filter_log = []
            filter_log.append("=== 高性能策略筛选过程 ===")
            filter_log.append(f"初始数据: {len(all_numbers):,}注 (00000-99999)")
            filter_log.append(f"预计算耗时: {precompute_time:.2f}秒")

            # 获取用户选择的策略
            strategies = self.filter_component.filter_strategies
            layer_count = 0

            print(f"📋 策略配置: {strategies}")

        except Exception as e:
            error_msg = [f"筛选初始化失败: {str(e)}"]
            print(f"❌ 筛选初始化失败: {e}")
            self.filter_component.set_filter_data([], error_msg, 0)
            return

        # 第1层：和值筛选（如果启用）
        if strategies['sum_filter']['enabled']:
            layer_count += 1
            count = strategies['sum_filter']['count']
            # 进度条已禁用，仅保留状态消息
            self.filter_component.update_progress(0, f"第{layer_count}层: 和值筛选...")
            excluded_sums = self.get_top_excluded_sums(count)
            if excluded_sums:
                before_count = len(all_numbers)
                all_numbers = self.filter_by_sums(all_numbers, excluded_sums)
                filter_log.append(f"\n第{layer_count}层 - 和值统计筛选:")
                filter_log.append(f"选择前{count}个热门和值，排除: {', '.join(map(str, excluded_sums))}")
                filter_log.append(f"筛选后: {before_count:,} → {len(all_numbers):,}注")

        # 第2层：位置数字筛选（如果启用）
        if strategies['position_digit_filter']['enabled']:
            layer_count += 1
            count = strategies['position_digit_filter']['count']
            mode = strategies['position_digit_filter'].get('mode', 'top_n')
            # 进度条已禁用，仅保留状态消息
            self.filter_component.update_progress(0, f"第{layer_count}层: 位置数字筛选...")
            excluded_position_digits = self.get_top_excluded_position_digits(count)
            if excluded_position_digits:
                before_count = len(all_numbers)
                all_numbers = self.filter_by_position_digits(all_numbers, excluded_position_digits)
                filter_log.append(f"\n第{layer_count}层 - 位置数字筛选:")
                if mode == 'top_n':
                    mode_text = "热门数字"
                elif mode == 'random':
                    mode_text = "随机数字"
                else:  # mode == 'bottom_n'
                    mode_text = "冷门数字"
                filter_log.append(f"每位置选择前{count}个{mode_text}，排除: {', '.join(excluded_position_digits)}")
                filter_log.append(f"筛选后: {before_count:,} → {len(all_numbers):,}注")

        # 第3层：大小组合筛选（如果启用）
        if strategies['size_combo_filter']['enabled']:
            layer_count += 1
            count = strategies['size_combo_filter']['count']
            mode = strategies['size_combo_filter'].get('mode', 'top_n')
            # 进度条已禁用，仅保留状态消息
            self.filter_component.update_progress(0, f"第{layer_count}层: 大小组合筛选...")
            excluded_size_patterns = self.get_top_excluded_size_patterns(count)
            if excluded_size_patterns:
                before_count = len(all_numbers)
                all_numbers = self.filter_by_size_patterns(all_numbers, excluded_size_patterns)
                filter_log.append(f"\n第{layer_count}层 - 大小组合筛选:")
                if mode == 'top_n':
                    mode_text = "热门组合"
                elif mode == 'random':
                    mode_text = "随机组合"
                else:  # mode == 'bottom_n'
                    mode_text = "冷门组合"
                filter_log.append(f"选择前{count}个{mode_text}，排除: {', '.join(excluded_size_patterns)}")
                filter_log.append(f"筛选后: {before_count:,} → {len(all_numbers):,}注")

        # 第4层：奇偶组合筛选（如果启用）
        if strategies['parity_combo_filter']['enabled']:
            layer_count += 1
            count = strategies['parity_combo_filter']['count']
            mode = strategies['parity_combo_filter'].get('mode', 'top_n')
            # 进度条已禁用，仅保留状态消息
            self.filter_component.update_progress(0, f"第{layer_count}层: 奇偶组合筛选...")
            excluded_parity_patterns = self.get_top_excluded_parity_patterns(count)
            if excluded_parity_patterns:
                before_count = len(all_numbers)
                all_numbers = self.filter_by_parity_patterns(all_numbers, excluded_parity_patterns)
                filter_log.append(f"\n第{layer_count}层 - 奇偶组合筛选:")
                if mode == 'top_n':
                    mode_text = "热门组合"
                elif mode == 'random':
                    mode_text = "随机组合"
                else:  # mode == 'bottom_n'
                    mode_text = "冷门组合"
                filter_log.append(f"选择前{count}个{mode_text}，排除: {', '.join(excluded_parity_patterns)}")
                filter_log.append(f"筛选后: {before_count:,} → {len(all_numbers):,}注")

        # 第5层：杀上期形态筛选（如果启用）
        if strategies['kill_previous_pattern_filter']['enabled'] and strategies['kill_previous_pattern_filter'].get('enable_filtering', False):
            layer_count += 1
            pattern_type = strategies['kill_previous_pattern_filter'].get('pattern_type', 'sum')
            excluded_pattern_value = strategies['kill_previous_pattern_filter'].get('excluded_pattern_value')

            if excluded_pattern_value is not None and pattern_type == 'sum':
                # 进度条已禁用，仅保留状态消息
                self.filter_component.update_progress(0, f"第{layer_count}层: 杀上期形态筛选...")
                before_count = len(all_numbers)
                all_numbers = self.filter_by_kill_previous_pattern(all_numbers, excluded_pattern_value)
                filter_log.append(f"\n第{layer_count}层 - 杀上期形态筛选:")
                consecutive_count = strategies['kill_previous_pattern_filter'].get('consecutive_count', 0)
                filter_threshold = strategies['kill_previous_pattern_filter'].get('filter_threshold', 10)
                filter_log.append(f"排除和值{excluded_pattern_value} (连续{consecutive_count}期未重复 > 阈值{filter_threshold}期)")
                filter_log.append(f"筛选后: {before_count:,} → {len(all_numbers):,}注")

        # 注意：数字连续未出现分析策略不参与筛选过程，仅用于分析展示

        # 如果没有启用任何筛选策略
        if layer_count == 0:
            filter_log.append("\n⚠️ 未启用任何筛选策略，返回全部数据")

        # 检查是否只启用了分析策略
        analysis_strategies = ['digit_absence_filter', 'latest_absence_filter']

        # 杀上期形态策略：如果启用了筛选功能，则视为筛选策略；否则视为分析策略
        kill_pattern_is_filter = (strategies['kill_previous_pattern_filter']['enabled'] and
                                 strategies['kill_previous_pattern_filter'].get('enable_filtering', False))

        enabled_filter_strategies = [key for key, info in strategies.items()
                                   if info['enabled'] and key not in analysis_strategies and
                                   (key != 'kill_previous_pattern_filter' or kill_pattern_is_filter)]
        enabled_analysis_strategies = [key for key in analysis_strategies
                                     if strategies[key]['enabled']]

        # 如果杀上期形态策略启用但未启用筛选功能，则视为分析策略
        if strategies['kill_previous_pattern_filter']['enabled'] and not kill_pattern_is_filter:
            enabled_analysis_strategies.append('kill_previous_pattern_filter')

        only_analysis_enabled = (len(enabled_analysis_strategies) > 0 and
                               len(enabled_filter_strategies) == 0)

        # 如果只启用了分析策略，显示分析结果而不是筛选结果
        if only_analysis_enabled:
            filter_log.append("\n📊 仅启用分析策略，显示数字统计分析结果")
            filter_log.append(f"📊 启用分析策略: {strategies['digit_absence_filter']['name']}")

            # 记录分析结束时间
            end_time = time.time()
            total_time = end_time - start_time
            filter_log.append(f"\n⏱️ 分析总耗时: {total_time:.2f}秒")

            # 清理缓存
            if hasattr(self, 'number_cache'):
                del self.number_cache
                print("🗑️ 已清理预计算缓存")

            # 执行启用的分析策略
            statistics_text = ""
            predicted_digit = "未知"
            predicted_digits = []

            if strategies['digit_absence_filter']['enabled']:
                print("🔍 执行数字连续未出现分析...")
                self.analyze_digit_absence()
                strategy = self.filter_component.filter_strategies.get('digit_absence_filter')
                if strategy:
                    statistics_text = strategy.get('statistics_text', '')
                    # 获取预测数字（支持多数字）
                    predicted_digits = strategy.get('predicted_digits', [])
                    predicted_digit = strategy.get('predicted_digit', '未知')

            if strategies['latest_absence_filter']['enabled']:
                print("🔍 执行最新未出现最少分析...")
                self.analyze_latest_absence()
                strategy = self.filter_component.filter_strategies.get('latest_absence_filter')
                if strategy:
                    if statistics_text:
                        statistics_text += "\n\n" + strategy.get('statistics_text', '')
                    else:
                        statistics_text = strategy.get('statistics_text', '')
                    # 如果有多个分析策略，使用最后一个的预测数字
                    predicted_digits = strategy.get('predicted_digits', [])
                    predicted_digit = strategy.get('predicted_digit', predicted_digit)

            if strategies['kill_previous_pattern_filter']['enabled']:
                print("🔍 执行杀上期形态分析...")
                self.analyze_kill_previous_pattern()
                strategy = self.filter_component.filter_strategies.get('kill_previous_pattern_filter')
                if strategy:
                    if statistics_text:
                        statistics_text += "\n\n" + strategy.get('statistics_text', '')
                    else:
                        statistics_text = strategy.get('statistics_text', '')

            # 显示分析结果而不是筛选结果
            # 传递多数字列表以支持多数字显示
            if predicted_digits:
                self.filter_component.set_analysis_data(filter_log, statistics_text, predicted_digit, predicted_digits)
            else:
                self.filter_component.set_analysis_data(filter_log, statistics_text, predicted_digit)
            self.filter_component.mark_filter_completed()
            return

        # 显示启用的筛选策略总结（排除纯分析策略）
        if enabled_filter_strategies:
            strategy_names = [strategies[key]['name'] for key in enabled_filter_strategies]
            filter_log.append(f"\n📊 本次启用筛选策略: {', '.join(strategy_names)}")

        # 显示启用的分析策略
        enabled_analysis_names = []
        if strategies['digit_absence_filter']['enabled']:
            enabled_analysis_names.append(strategies['digit_absence_filter']['name'])
        if strategies['latest_absence_filter']['enabled']:
            enabled_analysis_names.append(strategies['latest_absence_filter']['name'])

        if enabled_analysis_names:
            filter_log.append(f"📊 启用分析策略: {', '.join(enabled_analysis_names)} (仅分析展示，不参与筛选)")

        try:
            # 最终结果处理
            # 进度条已禁用，仅保留状态消息
            self.filter_component.update_progress(0, "生成最终结果...")
            final_filtered_numbers = sorted(list(all_numbers))
            filter_result_count = len(final_filtered_numbers)

            # 计算总体性能统计
            total_time = time.time() - start_time
            filter_log.append(f"\n=== 高性能筛选完成 ===")
            filter_log.append(f"最终剩余: {filter_result_count:,}注")
            filter_log.append(f"总耗时: {total_time:.2f}秒")
            filter_log.append(f"筛选效率: {100000/total_time:.0f}注/秒")

            # 性能等级评估
            if total_time < 2:
                performance_level = "🚀 极速"
            elif total_time < 5:
                performance_level = "⚡ 快速"
            elif total_time < 10:
                performance_level = "✅ 正常"
            else:
                performance_level = "🐌 较慢"

            filter_log.append(f"性能等级: {performance_level}")

            print(f"✅ 高性能筛选完成，剩余 {filter_result_count:,} 注")
            print(f"⚡ 总耗时: {total_time:.2f}秒，效率: {100000/total_time:.0f}注/秒，{performance_level}")

            # 完成进度
            # 进度条已禁用，仅保留状态消息
            self.filter_component.update_progress(0, "筛选完成")

            # 清理缓存以释放内存
            if hasattr(self, 'number_cache'):
                del self.number_cache
                print("🗑️ 已清理预计算缓存")

            # 如果启用了分析策略，确保分析已完成
            if strategies['digit_absence_filter']['enabled']:
                print("🔍 执行数字连续未出现分析...")
                self.analyze_digit_absence()

            if strategies['latest_absence_filter']['enabled']:
                print("🔍 执行最新未出现最少分析...")
                self.analyze_latest_absence()

            if strategies['kill_previous_pattern_filter']['enabled']:
                print("🔍 执行杀上期形态分析...")
                self.analyze_kill_previous_pattern()

            # 将数据传递给筛选结果组件
            self.filter_component.set_filter_data(final_filtered_numbers, filter_log, filter_result_count)

            # 标记筛选完成
            self.filter_component.mark_filter_completed()

        except Exception as e:
            error_msg = [f"筛选过程出错: {str(e)}"]
            print(f"❌ 筛选过程出错: {e}")
            # 清理缓存
            if hasattr(self, 'number_cache'):
                del self.number_cache
            self.filter_component.set_filter_data([], error_msg, 0)
            self.filter_component.update_filter_status("筛选失败", "red")

    def get_top_excluded_sums(self, count):
        """获取未出现期数排名前N的和值"""
        excluded_sums = []
        for i in range(min(count, self.sum_list.GetItemCount())):
            # 现在和值在第二列（索引1）
            sum_val = int(self.sum_list.GetItemText(i, 1))
            excluded_sums.append(sum_val)
        return excluded_sums

    def get_top_excluded_position_digits(self, count_per_position=1):
        """获取每个位置未出现期数排名前N或后N的数字"""
        strategy = self.filter_component.filter_strategies['position_digit_filter']
        mode = strategy.get('mode', 'top_n')

        if mode == 'random':
            return self.filter_component.get_random_position_digits(count_per_position)
        else:
            # 热门模式(top_n)或冷门模式(bottom_n)
            excluded_digits = []
            position_data = {}  # 记录每个位置的数字和期数

            # 收集每个位置的数据
            for i in range(self.position_list.GetItemCount()):
                # 现在位置数字在第二列（索引1），期数在第三列（索引2）
                pos_digit = self.position_list.GetItemText(i, 1)
                periods = int(self.position_list.GetItemText(i, 2))

                # 解析位置和数字
                if "第" in pos_digit and "位-" in pos_digit:
                    pos = int(pos_digit.split("第")[1].split("位-")[0])
                    digit = int(pos_digit.split("位-")[1])

                    if pos not in position_data:
                        position_data[pos] = []
                    position_data[pos].append((digit, periods))

            # 为每个位置选择前N个或后N个数字
            for pos in range(1, 6):
                if pos in position_data:
                    # 按期数排序
                    if mode == 'top_n':
                        # 前几个：选择未出现期数最多的（排序降序，取前几个）
                        sorted_digits = sorted(position_data[pos], key=lambda x: x[1], reverse=True)
                    else:  # mode == 'bottom_n'
                        # 后几个：选择未出现期数最少的（排序升序，取前几个）
                        sorted_digits = sorted(position_data[pos], key=lambda x: x[1], reverse=False)

                    for i in range(min(count_per_position, len(sorted_digits))):
                        digit, periods = sorted_digits[i]
                        excluded_digits.append(f"第{pos}位-{digit}")

            return excluded_digits

    def get_top_excluded_size_patterns(self, count):
        """获取未出现期数排名前N或后N的大小组合"""
        strategy = self.filter_component.filter_strategies['size_combo_filter']
        mode = strategy.get('mode', 'top_n')

        if mode == 'random':
            return self.filter_component.get_random_size_patterns(count)
        else:
            # 热门模式(top_n)或冷门模式(bottom_n)
            excluded_patterns = []
            total_items = self.size_list.GetItemCount()

            if mode == 'top_n':
                # 前几个：从列表开头选择（未出现期数最多的）
                for i in range(min(count, total_items)):
                    pattern = self.size_list.GetItemText(i, 1)
                    excluded_patterns.append(pattern)
            else:  # mode == 'bottom_n'
                # 后几个：从列表末尾选择（未出现期数最少的）
                start_index = max(0, total_items - count)
                for i in range(start_index, total_items):
                    pattern = self.size_list.GetItemText(i, 1)
                    excluded_patterns.append(pattern)

            return excluded_patterns

    def get_top_excluded_parity_patterns(self, count):
        """获取未出现期数排名前N或后N的奇偶组合"""
        strategy = self.filter_component.filter_strategies['parity_combo_filter']
        mode = strategy.get('mode', 'top_n')

        if mode == 'random':
            return self.filter_component.get_random_parity_patterns(count)
        else:
            # 热门模式(top_n)或冷门模式(bottom_n)
            excluded_patterns = []
            total_items = self.parity_list.GetItemCount()

            if mode == 'top_n':
                # 前几个：从列表开头选择（未出现期数最多的）
                for i in range(min(count, total_items)):
                    pattern = self.parity_list.GetItemText(i, 1)
                    excluded_patterns.append(pattern)
            else:  # mode == 'bottom_n'
                # 后几个：从列表末尾选择（未出现期数最少的）
                start_index = max(0, total_items - count)
                for i in range(start_index, total_items):
                    pattern = self.parity_list.GetItemText(i, 1)
                    excluded_patterns.append(pattern)

            return excluded_patterns

    def filter_by_sums(self, numbers, excluded_sums):
        """根据和值筛选号码（性能优化版）"""
        import time
        start_time = time.time()

        excluded_sums_set = set(excluded_sums)

        # 使用预计算的缓存数据，避免重复计算
        if hasattr(self, 'number_cache'):
            filtered_numbers = {num for num in numbers
                              if self.number_cache[num]['sum'] not in excluded_sums_set}
        else:
            # 回退到原始方法
            filtered_numbers = set()
            for num in numbers:
                digits = [int(d) for d in f"{num:05d}"]
                sum_val = sum(digits)
                if sum_val not in excluded_sums_set:
                    filtered_numbers.add(num)

        elapsed_time = time.time() - start_time
        print(f"⚡ 和值筛选耗时: {elapsed_time:.3f}秒")
        return filtered_numbers

    def filter_by_position_digits(self, numbers, excluded_position_digits):
        """根据位置数字筛选号码（性能优化版）"""
        import time
        start_time = time.time()

        # 解析排除的位置数字，支持每个位置多个数字
        excluded_dict = {}  # {position: set(digits)}
        for pos_digit in excluded_position_digits:
            if "第" in pos_digit and "位-" in pos_digit:
                try:
                    pos = int(pos_digit.split("第")[1].split("位-")[0])
                    digit = int(pos_digit.split("位-")[1])

                    if pos not in excluded_dict:
                        excluded_dict[pos] = set()
                    excluded_dict[pos].add(digit)
                except (ValueError, IndexError) as e:
                    print(f"解析位置数字失败: {pos_digit}, 错误: {e}")
                    continue

        print(f"位置数字筛选 - 排除规则: {excluded_dict}")

        # 使用预计算的缓存数据进行快速筛选
        if hasattr(self, 'number_cache'):
            filtered_numbers = set()
            for num in numbers:
                digits = self.number_cache[num]['digits']
                should_exclude = False

                # 检查每个位置是否包含被排除的数字
                for pos, excluded_digits in excluded_dict.items():
                    if 1 <= pos <= 5:  # 位置1-5对应索引0-4
                        if digits[pos-1] in excluded_digits:
                            should_exclude = True
                            break

                if not should_exclude:
                    filtered_numbers.add(num)
        else:
            # 回退到原始方法
            filtered_numbers = set()
            for num in numbers:
                digits = [int(d) for d in f"{num:05d}"]
                should_exclude = False

                for pos, excluded_digits in excluded_dict.items():
                    if 1 <= pos <= 5:
                        if digits[pos-1] in excluded_digits:
                            should_exclude = True
                            break

                if not should_exclude:
                    filtered_numbers.add(num)

        elapsed_time = time.time() - start_time
        print(f"⚡ 位置数字筛选耗时: {elapsed_time:.3f}秒")
        return filtered_numbers

    def filter_by_size_patterns(self, numbers, excluded_patterns):
        """根据大小组合筛选号码（性能优化版）"""
        import time
        start_time = time.time()

        excluded_patterns_set = set(excluded_patterns)

        # 使用预计算的缓存数据
        if hasattr(self, 'number_cache'):
            filtered_numbers = set()
            for num in numbers:
                digits = self.number_cache[num]['digits']

                # 生成大小模式
                pattern = ""
                for digit in digits:
                    if digit <= 4:
                        pattern += "小"
                    else:
                        pattern += "大"

                if pattern not in excluded_patterns_set:
                    filtered_numbers.add(num)
        else:
            # 回退到原始方法
            filtered_numbers = set()
            for num in numbers:
                digits = [int(d) for d in f"{num:05d}"]

                pattern = ""
                for digit in digits:
                    if digit <= 4:
                        pattern += "小"
                    else:
                        pattern += "大"

                if pattern not in excluded_patterns_set:
                    filtered_numbers.add(num)

        elapsed_time = time.time() - start_time
        print(f"⚡ 大小组合筛选耗时: {elapsed_time:.3f}秒")
        return filtered_numbers

    def filter_by_parity_patterns(self, numbers, excluded_patterns):
        """根据奇偶组合筛选号码（性能优化版）"""
        import time
        start_time = time.time()

        excluded_patterns_set = set(excluded_patterns)

        # 使用预计算的缓存数据
        if hasattr(self, 'number_cache'):
            filtered_numbers = set()
            for num in numbers:
                digits = self.number_cache[num]['digits']

                # 生成奇偶模式
                pattern = ""
                for digit in digits:
                    if digit % 2 == 0:
                        pattern += "偶"
                    else:
                        pattern += "奇"

                if pattern not in excluded_patterns_set:
                    filtered_numbers.add(num)
        else:
            # 回退到原始方法
            filtered_numbers = set()
            for num in numbers:
                digits = [int(d) for d in f"{num:05d}"]

                pattern = ""
                for digit in digits:
                    if digit % 2 == 0:
                        pattern += "偶"
                    else:
                        pattern += "奇"

                if pattern not in excluded_patterns_set:
                    filtered_numbers.add(num)

        elapsed_time = time.time() - start_time
        print(f"⚡ 奇偶组合筛选耗时: {elapsed_time:.3f}秒")
        return filtered_numbers

    def filter_by_kill_previous_pattern(self, numbers, excluded_sum_value):
        """根据杀上期形态筛选号码（排除指定和值的号码）"""
        import time
        start_time = time.time()

        print(f"🚫 开始杀上期形态筛选，排除和值: {excluded_sum_value}")

        # 使用预计算的缓存进行高效筛选
        filtered_numbers = set()
        for num in numbers:
            if self.number_cache[num]['sum'] != excluded_sum_value:
                filtered_numbers.add(num)

        print(f"🚫 杀上期形态筛选完成: {len(numbers):,} → {len(filtered_numbers):,}注")

        elapsed_time = time.time() - start_time
        print(f"⚡ 杀上期形态筛选耗时: {elapsed_time:.3f}秒")
        return filtered_numbers

    def start_auto_update(self):
        """启动自动更新机制"""
        # 创建定时器，每5秒检查一次文件变化
        self.auto_update_timer = wx.Timer(self)
        self.Bind(wx.EVT_TIMER, self.check_file_update, self.auto_update_timer)
        self.auto_update_timer.Start(5000)  # 5秒间隔

    def check_file_update(self, event):
        """检查文件是否有更新"""
        try:
            if os.path.exists(self.data_file_path):
                current_mtime = os.path.getmtime(self.data_file_path)
                if current_mtime > self.last_file_mtime:
                    # 文件已更新，重新加载数据
                    self.load_data()
                    print(f"检测到文件更新，已自动重新加载数据 - {time.strftime('%H:%M:%S')}")
        except Exception as e:
            print(f"检查文件更新时出错: {str(e)}")

    def stop_auto_update(self):
        """停止自动更新"""
        if self.auto_update_timer:
            self.auto_update_timer.Stop()
            self.auto_update_timer = None

    def on_close(self, event):
        """窗口关闭事件处理"""
        self.stop_auto_update()
        self.Destroy()

    def get_filter_component(self):
        """获取筛选结果组件 - 提供外部访问接口，与kill_number_display_wx.py保持一致"""
        return self.filter_component if hasattr(self, 'filter_component') else None

    def get_numbers_text(self):
        """获取筛选结果文本控件 - 提供外部访问接口，与kill_number_display_wx.py保持一致"""
        if hasattr(self, 'filter_component') and self.filter_component:
            return self.filter_component.numbers_text
        return None

    def get_filtered_numbers(self):
        """获取筛选后的号码列表 - 提供外部访问接口"""
        if hasattr(self, 'filter_component') and self.filter_component:
            return self.filter_component.get_filtered_numbers()
        return []

class LotteryDataAnalysisApp_Main(wx.App):
    def OnInit(self):
        frame = LotteryDataAnalysisApp()
        frame.Show()
        return True

if __name__ == '__main__':
    app = LotteryDataAnalysisApp_Main()
    app.MainLoop()
