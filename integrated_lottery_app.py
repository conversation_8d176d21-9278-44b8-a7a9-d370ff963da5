#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成的5星彩票分析系统
包含策略选择器和主要功能
"""

import wx
import os
import sys
from lottery_strategy_selector import LotteryStrategySelector

class IntegratedLotteryFrame(wx.Frame):
    def __init__(self):
        super().__init__(None, title="5星彩票分析系统", size=(1200, 800))
        
        self.strategy_selector = None
        self.selected_strategies = []
        
        self.init_ui()
        self.Center()
        
    def init_ui(self):
        """初始化用户界面"""
        panel = wx.Panel(self)
        main_sizer = wx.BoxSizer(wx.VERTICAL)
        
        # 标题
        title = wx.StaticText(panel, label="5星彩票分析系统")
        title_font = wx.Font(18, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_BOLD)
        title.SetFont(title_font)
        main_sizer.Add(title, 0, wx.ALL | wx.CENTER, 15)
        
        # 功能按钮区域
        btn_box = wx.StaticBox(panel, label="主要功能")
        btn_sizer = wx.StaticBoxSizer(btn_box, wx.VERTICAL)
        
        # 按钮网格
        grid_sizer = wx.GridSizer(2, 3, 10, 10)
        
        # 策略选择按钮
        strategy_btn = wx.Button(panel, label="杀码策略选择", size=(150, 50))
        strategy_btn.Bind(wx.EVT_BUTTON, self.on_open_strategy_selector)
        grid_sizer.Add(strategy_btn, 0, wx.EXPAND)
        
        # 号码生成按钮
        generate_btn = wx.Button(panel, label="号码生成", size=(150, 50))
        generate_btn.Bind(wx.EVT_BUTTON, self.on_generate_numbers)
        grid_sizer.Add(generate_btn, 0, wx.EXPAND)
        
        # 历史分析按钮
        analysis_btn = wx.Button(panel, label="历史分析", size=(150, 50))
        analysis_btn.Bind(wx.EVT_BUTTON, self.on_historical_analysis)
        grid_sizer.Add(analysis_btn, 0, wx.EXPAND)
        
        # 趋势预测按钮
        trend_btn = wx.Button(panel, label="趋势预测", size=(150, 50))
        trend_btn.Bind(wx.EVT_BUTTON, self.on_trend_prediction)
        grid_sizer.Add(trend_btn, 0, wx.EXPAND)
        
        # 数据管理按钮
        data_btn = wx.Button(panel, label="数据管理", size=(150, 50))
        data_btn.Bind(wx.EVT_BUTTON, self.on_data_management)
        grid_sizer.Add(data_btn, 0, wx.EXPAND)
        
        # 设置按钮
        settings_btn = wx.Button(panel, label="系统设置", size=(150, 50))
        settings_btn.Bind(wx.EVT_BUTTON, self.on_settings)
        grid_sizer.Add(settings_btn, 0, wx.EXPAND)
        
        btn_sizer.Add(grid_sizer, 0, wx.ALL | wx.EXPAND, 10)
        main_sizer.Add(btn_sizer, 0, wx.ALL | wx.EXPAND, 20)
        
        # 当前策略显示区域
        strategy_box = wx.StaticBox(panel, label="当前选择的杀码策略")
        strategy_display_sizer = wx.StaticBoxSizer(strategy_box, wx.VERTICAL)
        
        self.strategy_display = wx.TextCtrl(panel, style=wx.TE_MULTILINE | wx.TE_READONLY, size=(-1, 150))
        self.strategy_display.SetValue("未选择任何策略")
        strategy_display_sizer.Add(self.strategy_display, 1, wx.ALL | wx.EXPAND, 5)
        
        main_sizer.Add(strategy_display_sizer, 1, wx.ALL | wx.EXPAND, 20)
        
        # 结果显示区域
        result_box = wx.StaticBox(panel, label="分析结果")
        result_sizer = wx.StaticBoxSizer(result_box, wx.VERTICAL)
        
        self.result_display = wx.TextCtrl(panel, style=wx.TE_MULTILINE | wx.TE_READONLY, size=(-1, 200))
        self.result_display.SetValue("等待操作...")
        result_sizer.Add(self.result_display, 1, wx.ALL | wx.EXPAND, 5)
        
        main_sizer.Add(result_sizer, 1, wx.ALL | wx.EXPAND, 20)
        
        # 状态栏
        self.status_text = wx.StaticText(panel, label="系统就绪")
        main_sizer.Add(self.status_text, 0, wx.ALL | wx.EXPAND, 10)
        
        panel.SetSizer(main_sizer)
        
    def on_open_strategy_selector(self, event):
        """打开策略选择器"""
        if self.strategy_selector is None or not self.strategy_selector.IsShown():
            self.strategy_selector = LotteryStrategySelector()
            self.strategy_selector.Show()
            
            # 绑定关闭事件以获取选择的策略
            self.strategy_selector.Bind(wx.EVT_CLOSE, self.on_strategy_selector_close)
        else:
            self.strategy_selector.Raise()
            
    def on_strategy_selector_close(self, event):
        """策略选择器关闭事件"""
        if self.strategy_selector:
            # 获取选择的策略
            self.selected_strategies = self.strategy_selector.get_selected_strategies()
            self.update_strategy_display()
            
        event.Skip()  # 允许窗口正常关闭
        
    def update_strategy_display(self):
        """更新策略显示"""
        if not self.selected_strategies:
            self.strategy_display.SetValue("未选择任何策略")
            return
            
        text_lines = [f"已选择 {len(self.selected_strategies)} 个杀码策略:"]
        for i, strategy in enumerate(self.selected_strategies, 1):
            text_lines.append(f"{i}. {strategy}")
            
        self.strategy_display.SetValue("\n".join(text_lines))
        self.status_text.SetLabel(f"已选择 {len(self.selected_strategies)} 个策略")
        
    def on_generate_numbers(self, event):
        """号码生成"""
        if not self.selected_strategies:
            wx.MessageBox("请先选择杀码策略", "提示", wx.OK | wx.ICON_INFORMATION)
            return
            
        # 这里可以集成号码生成逻辑
        result = f"基于以下策略生成号码:\n"
        result += "\n".join(f"• {strategy}" for strategy in self.selected_strategies)
        result += "\n\n生成的推荐号码:\n"
        result += "12345, 67890, 13579, 24680, 11223\n"
        result += "(示例数据，实际应用中会根据策略计算)"
        
        self.result_display.SetValue(result)
        self.status_text.SetLabel("号码生成完成")
        
    def on_historical_analysis(self, event):
        """历史分析"""
        result = "历史数据分析结果:\n\n"
        result += "• 数据期数: 100期\n"
        result += "• 和值范围: 10-35\n"
        result += "• 跨度范围: 3-9\n"
        result += "• 奇偶比分布: 均匀\n"
        result += "• 连号出现频率: 65%\n"
        result += "• 重号出现频率: 25%\n"
        
        self.result_display.SetValue(result)
        self.status_text.SetLabel("历史分析完成")
        
    def on_trend_prediction(self, event):
        """趋势预测"""
        result = "趋势预测分析:\n\n"
        result += "基于最近20期数据预测:\n"
        result += "• 下期和值预测: 18-25\n"
        result += "• 下期跨度预测: 5-7\n"
        result += "• 奇偶比预测: 3:2或2:3\n"
        result += "• 连号概率: 70%\n"
        result += "• 重号概率: 20%\n"
        
        self.result_display.SetValue(result)
        self.status_text.SetLabel("趋势预测完成")
        
    def on_data_management(self, event):
        """数据管理"""
        result = "数据管理功能:\n\n"
        result += "• 历史数据文件: results.csv\n"
        result += "• 数据完整性: 良好\n"
        result += "• 最新更新: 今天\n"
        result += "• 数据备份: 已启用\n"
        
        self.result_display.SetValue(result)
        self.status_text.SetLabel("数据管理信息已显示")
        
    def on_settings(self, event):
        """系统设置"""
        result = "系统设置:\n\n"
        result += "• 界面主题: 默认\n"
        result += "• 数据源: results.csv\n"
        result += "• 自动刷新: 启用\n"
        result += "• 备份间隔: 每日\n"
        
        self.result_display.SetValue(result)
        self.status_text.SetLabel("系统设置已显示")


class IntegratedLotteryApp(wx.App):
    def OnInit(self):
        frame = IntegratedLotteryFrame()
        frame.Show()
        return True


def main():
    """主函数"""
    app = IntegratedLotteryApp()
    app.MainLoop()


if __name__ == '__main__':
    main()
